<?php
/**
 * Teste para verificar se a busca de colaboradores na página de emails
 * está aplicando o filtro de usuários inativos
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Verificar permissões
checkPageAccess(['gestor', 'admin']);

echo "<h1>🧪 Teste: Filtro de Usuários Inativos na Busca de E-mails</h1>\n";

// Simular a mesma lógica do ajax_colaboradores.php
try {
    // Inicializar API da Intranet
    $api = new IntranetAPI();
    $usuarios_intranet_todos = $api->listarUsuarios(false, false); // Todos os usuários, sem cache
    $usuarios_intranet_ativos = $api->listarUsuariosAtivos(false); // Apenas ativos, sem cache

    echo "<h2>1. 📊 Dados da API</h2>\n";
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>API Status:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Total usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
    echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
    echo "<li><strong>Usuários inativos:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
    echo "</ul>";
    echo "</div>";

    // Criar mapas de usuários por CPF
    $mapa_usuarios_ativos = [];
    $mapa_usuarios_todos = [];

    if ($usuarios_intranet_ativos !== false) {
        foreach ($usuarios_intranet_ativos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
            }
        }
    }

    if ($usuarios_intranet_todos !== false) {
        foreach ($usuarios_intranet_todos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
            }
        }
    }

    echo "<h2>2. 🔍 Teste de Busca Específica</h2>\n";

    // Testar busca por colaboradores específicos
    $termos_teste = ['ALICE', 'CLESIO', 'MARIA'];

    foreach ($termos_teste as $termo) {
        echo "<h3>🔍 Busca por: '$termo'</h3>";

        // Query similar ao ajax_colaboradores.php
        $searchTerm = "%$termo%";
        $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];

        $sql = "
            SELECT
                cpf,
                MAX(usuario) as nome,
                MAX(email) as email,
                MAX(funcao) as funcao,
                MAX(codigo_unidade) as codigo_unidade,
                COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                                   AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                   THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
                COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                                   AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                   THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
            FROM edu_relatorio_educacao
            WHERE " . implode(" AND ", $whereConditions) . "
            GROUP BY cpf
            ORDER BY MAX(usuario)
            LIMIT 10
        ";

        $stmt = $pdo_edu->prepare($sql);
        $stmt->execute($params);
        $todos_colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Colaboradores encontrados no banco:</strong> " . count($todos_colaboradores) . "</p>";
        echo "</div>";

        // Aplicar filtro de usuários inativos
        $colaboradores_filtrados = [];
        $total_filtrados = 0;

        foreach ($todos_colaboradores as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            
            $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
            $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
            
            // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
            if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
                $total_filtrados++;
                continue; // Usuário inativo - PULAR
            }
            
            // Incluir colaborador na lista filtrada
            $colaboradores_filtrados[] = $colaborador;
        }

        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 5px;'>CPF</th>";
        echo "<th style='padding: 5px;'>Nome</th>";
        echo "<th style='padding: 5px;'>Email</th>";
        echo "<th style='padding: 5px;'>Status</th>";
        echo "<th style='padding: 5px;'>Resultado</th>";
        echo "</tr>";

        foreach ($todos_colaboradores as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            
            $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
            $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
            
            $status = '';
            $resultado = '';
            $cor_linha = '';
            
            if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
                $status = 'INATIVO';
                $resultado = '❌ FILTRADO';
                $cor_linha = '#f8d7da';
            } elseif ($usuario_intranet_ativo) {
                $status = 'ATIVO';
                $resultado = '✅ INCLUÍDO';
                $cor_linha = '#d4edda';
            } else {
                $status = 'NÃO ENCONTRADO';
                $resultado = '✅ INCLUÍDO';
                $cor_linha = '#fff3cd';
            }
            
            echo "<tr style='background-color: $cor_linha;'>";
            echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['nome'], 0, 25)) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['email'], 0, 25)) . "</td>";
            echo "<td style='padding: 5px;'><strong>$status</strong></td>";
            echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
            echo "</tr>";
        }

        echo "</table>";
        echo "</div>";

        echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📊 Resumo para '$termo':</strong></p>";
        echo "<ul>";
        echo "<li><strong>Encontrados no banco:</strong> " . count($todos_colaboradores) . "</li>";
        echo "<li><strong>Filtrados (inativos):</strong> $total_filtrados</li>";
        echo "<li><strong>Que aparecerão na busca:</strong> " . count($colaboradores_filtrados) . "</li>";
        echo "</ul>";
        echo "</div>";
    }

    echo "<h2>3. 🧪 Teste da API AJAX</h2>\n";

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ Como Testar na Página de E-mails:</h3>";
    echo "<ol>";
    echo "<li><strong>Acesse:</strong> <a href='emails.php'>emails.php</a></li>";
    echo "<li><strong>Vá para a aba 'Envio Individual'</strong></li>";
    echo "<li><strong>Digite 'ALICE' no campo de busca</strong></li>";
    echo "<li><strong>Verifique se ALICE BEATRIZ não aparece</strong> (deve estar filtrada)</li>";
    echo "<li><strong>Digite 'CLESIO' no campo de busca</strong></li>";
    echo "<li><strong>Verifique se CLESIO não aparece</strong> (deve estar filtrado)</li>";
    echo "</ol>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔧 Teste Direto da API:</h3>";
    echo "<p>Você também pode testar diretamente a API AJAX:</p>";
    echo "<ul>";
    echo "<li><a href='ajax_colaboradores.php?search=ALICE&limit=10' target='_blank'>Buscar 'ALICE'</a></li>";
    echo "<li><a href='ajax_colaboradores.php?search=CLESIO&limit=10' target='_blank'>Buscar 'CLESIO'</a></li>";
    echo "<li><a href='ajax_colaboradores.php?search=MARIA&limit=10' target='_blank'>Buscar 'MARIA'</a></li>";
    echo "</ul>";
    echo "<p><strong>Resultado esperado:</strong> Usuários inativos não devem aparecer nos resultados JSON.</p>";
    echo "</div>";

    echo "<h2>4. 🎯 Resultado Esperado</h2>\n";

    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎯 Na página de e-mails:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Usuários ativos:</strong> Devem aparecer na busca normalmente</li>";
    echo "<li>✅ <strong>Usuários não encontrados na Intranet:</strong> Devem aparecer na busca</li>";
    echo "<li>❌ <strong>Usuários inativos na Intranet:</strong> NÃO devem aparecer na busca</li>";
    echo "<li>❌ <strong>ALICE BEATRIZ e CLESIO:</strong> NÃO devem aparecer (se inativos)</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
