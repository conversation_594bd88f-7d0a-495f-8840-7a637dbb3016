<?php
/**
 * Teste final para verificar se todas as estatísticas estão corretas e consistentes
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/filtro_usuarios_inativos.php';

echo "<h1>🎯 Teste Final: Estatísticas Consistentes</h1>\n";

// Limpar cache
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

echo "<h2>1. 📊 Estatísticas Centralizadas</h2>\n";

// Calcular estatísticas com a função centralizada
$estatisticas_completas = calcularEstatisticasConsistentes([], [], true, true);

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Estatísticas Calculadas pela Função Centralizada:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Métrica</th>";
echo "<th style='padding: 10px;'>Valor</th>";
echo "<th style='padding: 10px;'>Descrição</th>";
echo "</tr>";

$metricas_detalhadas = [
    'total_colaboradores' => ['Colaboradores', 'Total de colaboradores ativos + não encontrados na Intranet'],
    'trilhas_disponiveis' => ['Trilhas Disponíveis', 'Trilhas únicas cadastradas no sistema'],
    'cursos_cadastrados' => ['Cursos Cadastrados', 'Cursos únicos cadastrados no sistema'],
    'total_trilhas_atribuidas' => ['Trilhas Atribuídas', 'Total de trilhas atribuídas aos colaboradores'],
    'total_cursos_atribuidos' => ['Cursos Atribuídos', 'Total de cursos atribuídos aos colaboradores'],
    'cursos_aprovados' => ['Cursos Aprovados', 'Cursos com aprovação = "Sim"'],
    'cursos_concluidos' => ['Cursos Concluídos', 'Cursos com data de conclusão preenchida'],
    'cursos_em_andamento' => ['Cursos em Andamento', 'Estimativa: Atribuídos - Concluídos'],
    'cursos_vencidos' => ['Cursos Vencidos', 'Estimativa baseada em aprovações'],
    'media_aproveitamento' => ['Média de Aproveitamento', 'Média de aproveitamento dos colaboradores']
];

foreach ($metricas_detalhadas as $key => $info) {
    $valor = $estatisticas_completas[$key];
    if ($key == 'media_aproveitamento') {
        $valor = number_format($valor, 1) . '%';
    } else {
        $valor = number_format($valor);
    }
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>{$info[0]}</strong></td>";
    echo "<td style='padding: 10px; text-align: center;'><strong>$valor</strong></td>";
    echo "<td style='padding: 10px; font-size: 12px;'>{$info[1]}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<h2>2. 📊 Comparação com Páginas</h2>\n";

// Simular o que cada página deve mostrar
$pagina_analise = [
    'Colaboradores' => $estatisticas_completas['total_colaboradores'],
    'Trilhas Disponíveis' => $estatisticas_completas['trilhas_disponiveis'],
    'Cursos Cadastrados' => $estatisticas_completas['cursos_cadastrados'],
    'Cursos Atribuídos' => $estatisticas_completas['total_cursos_atribuidos'],
    'Cursos Concluídos' => $estatisticas_completas['cursos_concluidos'],
    'Cursos em Andamento' => $estatisticas_completas['cursos_em_andamento'],
    'Cursos Vencidos' => $estatisticas_completas['cursos_vencidos'],
    'Média de Aproveitamento' => number_format($estatisticas_completas['media_aproveitamento'], 1) . '%'
];

$pagina_relatorios = [
    'Colaboradores' => $estatisticas_completas['total_colaboradores'],
    'Trilhas' => $estatisticas_completas['total_trilhas_atribuidas'],
    'Cursos' => $estatisticas_completas['total_cursos_atribuidos'],
    'Aprovações' => $estatisticas_completas['cursos_aprovados']
];

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Página de Análise de Colaboradores deve mostrar:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
foreach ($pagina_analise as $nome => $valor) {
    echo "<div style='background: #28a745; color: white; padding: 10px; border-radius: 5px; text-align: center; min-width: 120px;'>";
    echo "<div style='font-size: 18px; font-weight: bold;'>$valor</div>";
    echo "<div style='font-size: 12px;'>$nome</div>";
    echo "</div>";
}
echo "</div>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Página de Relatórios deve mostrar:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
foreach ($pagina_relatorios as $nome => $valor) {
    echo "<div style='background: #007bff; color: white; padding: 10px; border-radius: 5px; text-align: center; min-width: 120px;'>";
    echo "<div style='font-size: 18px; font-weight: bold;'>$valor</div>";
    echo "<div style='font-size: 12px;'>$nome</div>";
    echo "</div>";
}
echo "</div>";
echo "</div>";

echo "<h2>3. 🔗 Teste das Páginas Reais</h2>\n";

$timestamp = time();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Teste Manual - Clique nos links abaixo:</h3>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=$timestamp' target='_blank' ";
echo "style='background: #28a745; color: white; padding: 15px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px; font-weight: bold;'>";
echo "📊 ANÁLISE DE COLABORADORES</a>";
echo "<a href='relatorios.php?nocache=$timestamp' target='_blank' ";
echo "style='background: #007bff; color: white; padding: 15px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>";
echo "📋 RELATÓRIOS</a>";
echo "</p>";
echo "</div>";

echo "<h2>4. ✅ Checklist de Verificação</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Verificações Obrigatórias:</h3>";
echo "<ol>";
echo "<li>☐ <strong>Colaboradores:</strong> Ambas as páginas devem mostrar <strong>" . number_format($estatisticas_completas['total_colaboradores']) . "</strong></li>";
echo "<li>☐ <strong>Análise - Trilhas:</strong> Deve mostrar <strong>" . number_format($estatisticas_completas['trilhas_disponiveis']) . "</strong> (trilhas disponíveis)</li>";
echo "<li>☐ <strong>Relatórios - Trilhas:</strong> Deve mostrar <strong>" . number_format($estatisticas_completas['total_trilhas_atribuidas']) . "</strong> (trilhas atribuídas)</li>";
echo "<li>☐ <strong>Análise - Cursos:</strong> Deve mostrar <strong>" . number_format($estatisticas_completas['cursos_cadastrados']) . "</strong> (cursos cadastrados)</li>";
echo "<li>☐ <strong>Relatórios - Cursos:</strong> Deve mostrar <strong>" . number_format($estatisticas_completas['total_cursos_atribuidos']) . "</strong> (cursos atribuídos)</li>";
echo "<li>☐ <strong>Média de Aproveitamento:</strong> Deve ser consistente em ambas as páginas</li>";
echo "</ol>";
echo "</div>";

echo "<h2>5. 🎯 Resultado Final Esperado</h2>\n";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Após a correção, as páginas devem mostrar:</h3>";

echo "<h4>📊 Análise de Colaboradores:</h4>";
echo "<ul>";
foreach ($pagina_analise as $nome => $valor) {
    echo "<li><strong>$valor</strong> $nome</li>";
}
echo "</ul>";

echo "<h4>📋 Relatórios:</h4>";
echo "<ul>";
foreach ($pagina_relatorios as $nome => $valor) {
    echo "<li><strong>$valor</strong> $nome</li>";
}
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚠️ Se houver divergências:</h3>";
echo "<ul>";
echo "<li>Verifique se o cache foi limpo completamente</li>";
echo "<li>Abra as páginas em aba anônima</li>";
echo "<li>Verifique se há erros no console do navegador</li>";
echo "<li>Verifique se a função <code>calcularEstatisticasConsistentes()</code> está sendo chamada</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Informações Técnicas:</h3>";
echo "<ul>";
echo "<li><strong>Função centralizada:</strong> <code>calcularEstatisticasConsistentes()</code></li>";
echo "<li><strong>Filtro aplicado:</strong> Usuários inativos da Intranet removidos</li>";
echo "<li><strong>Cache limpo:</strong> $cache_files_removed arquivos removidos</li>";
echo "<li><strong>Timestamp:</strong> $timestamp</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
