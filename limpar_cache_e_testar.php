<?php
/**
 * Script para limpar cache e testar estatísticas
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/filtro_usuarios_inativos.php';

echo "<h1>🧹 Limpar Cache e Testar</h1>\n";

// 1. Limpar cache completamente
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// 2. Testar função centralizada
echo "<h2>📊 Teste da Função Centralizada</h2>\n";

$estatisticas = calcularEstatisticasConsistentes([], [], true, true);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Estatísticas Recalculadas:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Métrica</th>";
echo "<th style='padding: 10px;'>Valor</th>";
echo "</tr>";

$metricas = [
    'total_colaboradores' => 'Colaboradores',
    'trilhas_disponiveis' => 'Trilhas Disponíveis',
    'cursos_cadastrados' => 'Cursos Cadastrados',
    'total_cursos_atribuidos' => 'Cursos Atribuídos',
    'cursos_concluidos' => 'Cursos Concluídos',
    'cursos_aprovados' => 'Cursos Aprovados',
    'cursos_em_andamento' => 'Cursos em Andamento',
    'cursos_vencidos' => 'Cursos Vencidos',
    'media_aproveitamento' => 'Média de Aproveitamento'
];

foreach ($metricas as $key => $nome) {
    $valor = $estatisticas[$key];
    if ($key == 'media_aproveitamento') {
        $valor = number_format($valor, 1) . '%';
    } else {
        $valor = number_format($valor);
    }
    
    $cor = ($key == 'cursos_vencidos' && $estatisticas[$key] > 0) ? '#fff3cd' : '#f8f9fa';
    
    echo "<tr style='background-color: $cor;'>";
    echo "<td style='padding: 10px;'><strong>$nome</strong></td>";
    echo "<td style='padding: 10px; text-align: center;'><strong>$valor</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// 3. Verificar dados brutos
echo "<h2>🔍 Verificação de Dados Brutos</h2>\n";

$query_dados = "
    SELECT 
        COUNT(DISTINCT CONCAT(cpf, '|', codigo_trilha, '|', codigo_recurso)) as total_cursos,
        COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(cpf, '|', codigo_trilha, '|', codigo_recurso) END) as total_concluidos,
        COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(cpf, '|', codigo_trilha, '|', codigo_recurso) END) as total_aprovados,
        COUNT(DISTINCT CASE WHEN (data_conclusao IS NULL OR data_conclusao = '0000-00-00') AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN CONCAT(cpf, '|', codigo_trilha, '|', codigo_recurso) END) as possiveis_vencidos
    FROM edu_relatorio_educacao
";

$stmt_dados = $pdo_edu->prepare($query_dados);
$stmt_dados->execute();
$dados_brutos = $stmt_dados->fetch();

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Dados Brutos da Base:</h3>";
echo "<ul>";
echo "<li><strong>Total de cursos únicos:</strong> " . number_format($dados_brutos['total_cursos']) . "</li>";
echo "<li><strong>Cursos concluídos:</strong> " . number_format($dados_brutos['total_concluidos']) . "</li>";
echo "<li><strong>Cursos aprovados:</strong> " . number_format($dados_brutos['total_aprovados']) . "</li>";
echo "<li><strong>Possíveis vencidos:</strong> " . number_format($dados_brutos['possiveis_vencidos']) . "</li>";
echo "</ul>";

// Calcular estimativas
$estimativa_vencidos_1 = max(0, $dados_brutos['total_cursos'] - $dados_brutos['total_concluidos']);
$estimativa_vencidos_2 = max(0, $dados_brutos['total_cursos'] - $dados_brutos['total_aprovados']);
$estimativa_vencidos_3 = $dados_brutos['possiveis_vencidos'];

echo "<h4>🧮 Estimativas de Cursos Vencidos:</h4>";
echo "<ul>";
echo "<li><strong>Método 1 (Total - Concluídos):</strong> " . number_format($estimativa_vencidos_1) . "</li>";
echo "<li><strong>Método 2 (Total - Aprovados):</strong> " . number_format($estimativa_vencidos_2) . "</li>";
echo "<li><strong>Método 3 (Não concluídos + Não aprovados):</strong> " . number_format($estimativa_vencidos_3) . "</li>";
echo "<li><strong>Função centralizada:</strong> " . number_format($estatisticas['cursos_vencidos']) . "</li>";
echo "</ul>";
echo "</div>";

// 4. Testar com colaboradores filtrados
echo "<h2>👥 Teste com Filtro de Colaboradores</h2>\n";

$query_colaboradores = "
    SELECT
        cpf,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as cursos_colaborador,
        COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as concluidos_colaborador,
        COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as aprovados_colaborador
    FROM edu_relatorio_educacao
    GROUP BY cpf
    LIMIT 5
";

$stmt_colab = $pdo_edu->prepare($query_colaboradores);
$stmt_colab->execute();
$amostra_colaboradores = $stmt_colab->fetchAll();

// Aplicar filtro
$colaboradores_filtrados = aplicarFiltroUsuariosInativos($amostra_colaboradores, true);

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>👥 Amostra de Colaboradores:</h3>";
echo "<p><strong>Antes do filtro:</strong> " . count($amostra_colaboradores) . " colaboradores</p>";
echo "<p><strong>Depois do filtro:</strong> " . count($colaboradores_filtrados) . " colaboradores</p>";
echo "<p><strong>Filtrados:</strong> " . (count($amostra_colaboradores) - count($colaboradores_filtrados)) . " colaboradores</p>";
echo "</div>";

// 5. Links para teste
$timestamp = time();

echo "<h2>🔗 Teste das Páginas</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Teste agora com cache limpo:</h3>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=$timestamp&force_refresh=1' target='_blank' ";
echo "style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>";
echo "📊 Análise de Colaboradores</a>";
echo "<a href='relatorios.php?nocache=$timestamp&force_refresh=1' target='_blank' ";
echo "style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>";
echo "📋 Relatórios</a>";
echo "</p>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Resultado Esperado:</h3>";
echo "<p><strong>Cursos Vencidos deve mostrar:</strong> " . number_format($estatisticas['cursos_vencidos']) . " (não mais 0)</p>";
echo "<p><strong>Se ainda aparecer 0, pode ser porque:</strong></p>";
echo "<ul>";
echo "<li>Cache do navegador não foi limpo</li>";
echo "<li>Há um cache adicional na aplicação</li>";
echo "<li>A lógica de cálculo precisa ser ajustada</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
