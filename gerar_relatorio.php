<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Configurações de performance para relatórios
set_time_limit(300); // 5 minutos
ini_set('memory_limit', '512M'); // Aumentar limite de memória

// Verificar permissões
checkEduPageAccess(['comum', 'gestor', 'admin']);

// Inicializar API da Intranet
$api = new IntranetAPI();
$usuarios_intranet = $api->listarUsuarios();
$agencias_intranet = $api->listarAgencias();

// Criar mapas de dados da intranet
$mapa_usuarios_cpf = [];
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
}

$mapa_agencias = [];
if ($agencias_intranet !== false) {
    foreach ($agencias_intranet as $agencia) {
        if (!empty($agencia['id'])) {
            $mapa_agencias[$agencia['id']] = $agencia;
        }
    }
}

// Obter parâmetros
$tipo_relatorio = $_GET['tipo'] ?? '';
$filtros = [
    'pa' => $_GET['pa'] ?? '',
    'trilha' => $_GET['trilha'] ?? '',
    'trilha_obrigatoria' => $_GET['trilha_obrigatoria'] ?? '',
    'funcao' => $_GET['funcao'] ?? '',
    'periodo' => $_GET['periodo'] ?? '',
    'curso' => $_GET['curso'] ?? '',
    'status' => $_GET['status'] ?? '',
    'periodo_analise' => $_GET['periodo_analise'] ?? '',
    'agrupamento' => $_GET['agrupamento'] ?? ''
];

// Buscar configurações de prazos personalizados
$stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
$prazos_config = [];
foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
    $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
    $prazos_config[$key] = $config;
}

// Buscar trilhas obrigatórias (com verificação se a tabela existe)
$trilhas_obrigatorias = [];
try {
    $stmt_trilhas_obrigatorias = $pdo_edu->query("SELECT codigo_trilha, trilha, obrigatoria FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1");
    foreach ($stmt_trilhas_obrigatorias->fetchAll(PDO::FETCH_ASSOC) as $trilha_obrig) {
        $trilhas_obrigatorias[$trilha_obrig['codigo_trilha']] = $trilha_obrig;
    }
} catch (PDOException $e) {
    // Tabela ainda não existe, continuar sem informações de trilhas obrigatórias
    $trilhas_obrigatorias = [];
}

// Função para verificar se uma trilha é obrigatória
function isTrilhaObrigatoria($codigo_trilha, $trilhas_obrigatorias) {
    return isset($trilhas_obrigatorias[$codigo_trilha]);
}

// Função para verificar se um curso está no período especificado
function verificarCursoNoPeriodo($prazo_curso, $periodo) {
    if (empty($periodo) || empty($prazo_curso)) return true;
    
    $hoje = new DateTime();
    
    switch ($periodo) {
        case 'mes_atual':
            return $prazo_curso->format('Y-m') === $hoje->format('Y-m');
        case 'proximo_mes':
            $proximo_mes = (clone $hoje)->modify('+1 month');
            return $prazo_curso->format('Y-m') === $proximo_mes->format('Y-m');
        case 'proximos_30_dias':
            $limite = (clone $hoje)->modify('+30 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;
        case 'proximos_60_dias':
            $limite = (clone $hoje)->modify('+60 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;
        case 'proximos_90_dias':
            $limite = (clone $hoje)->modify('+90 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;
        case 'vencidos_30_dias':
            $limite = (clone $hoje)->modify('-30 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;
        case 'vencidos_60_dias':
            $limite = (clone $hoje)->modify('-60 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;
        case 'vencidos_90_dias':
            $limite = (clone $hoje)->modify('-90 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;
        default:
            return true;
    }
}

// Função para buscar cursos de um colaborador com prazos calculados
function buscarCursosColaborador($cpf, $pdo, $prazos_config) {
    $query = "
        SELECT
            codigo_trilha, trilha, codigo_recurso, recurso, aprovacao,
            data_conclusao, concluir_trilha_ate, data_admissao, nota_recurso,
            aproveitamento, validade_recurso, andamento_etapa, carga_horaria_recurso
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY trilha, recurso
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$cpf]);
    $cursos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calcular prazo para cada curso
    foreach ($cursos as &$curso) {
        $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        
        if (isset($prazos_config[$key])) {
            $data_corte = '2023-01-01';
            $elegivel_prazo_personalizado = ($curso['data_admissao'] > $data_corte);
            
            if ($elegivel_prazo_personalizado) {
                $curso['prazo_calculado'] = calcularPrazoPersonalizado(
                    $cpf, $curso['codigo_trilha'], $curso['codigo_recurso'],
                    $curso['data_admissao'], $curso['concluir_trilha_ate'], $pdo
                );
                $curso['prazo_personalizado'] = true;
            } else {
                $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
                $curso['prazo_personalizado'] = false;
            }
        } else {
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                $curso['prazo_calculado'] = null;
            } else {
                $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
            }
            $curso['prazo_personalizado'] = false;
        }
        
        // Calcular status do prazo
        if ($curso['prazo_calculado'] === null) {
            $curso['status_prazo'] = 'sem_prazo';
            $curso['dias_prazo'] = null;
        } elseif (!empty($curso['prazo_calculado'])) {
            $hoje = new DateTime();
            $prazo = new DateTime($curso['prazo_calculado']);
            $diff = $hoje->diff($prazo);
            
            if ($prazo < $hoje) {
                $curso['status_prazo'] = 'vencido';
                $curso['dias_prazo'] = -$diff->days;
            } elseif ($diff->days <= 30) {
                $curso['status_prazo'] = 'a_vencer';
                $curso['dias_prazo'] = $diff->days;
            } else {
                $curso['status_prazo'] = 'em_dia';
                $curso['dias_prazo'] = $diff->days;
            }
        } else {
            $curso['status_prazo'] = 'sem_prazo';
            $curso['dias_prazo'] = null;
        }
    }
    
    return $cursos;
}

// Função para calcular prazo personalizado (simplificada)
function calcularPrazoPersonalizado($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";
    
    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        return $prazo_padrao;
    }
    
    $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
    $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;
    
    if ($primeiro_prazo <= 0) {
        return $prazo_padrao;
    }
    
    // Verificar se já houve conclusões anteriores
    $query_conclusoes = "
        SELECT data_conclusao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
        AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
        ORDER BY data_conclusao DESC
        LIMIT 1
    ";
    
    $stmt_conclusoes = $pdo->prepare($query_conclusoes);
    $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
    $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);
    
    try {
        if ($ultima_conclusao) {
            if ($renovacao_prazo <= 0) {
                return null; // Curso concluído e sem renovação
            }
            $data_base = new DateTime($ultima_conclusao['data_conclusao']);
            $data_base->add(new DateInterval('P' . $renovacao_prazo . 'D'));
        } else {
            $data_base = new DateTime($data_admissao);
            $data_base->add(new DateInterval('P' . $primeiro_prazo . 'D'));
        }
        
        return $data_base->format('Y-m-d');
    } catch (Exception $e) {
        return $prazo_padrao;
    }
}

// Função para formatar CPF
function formatarCpf($cpf) {
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

// Definir headers para download Excel
header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment; filename="relatorio_' . $tipo_relatorio . '_' . date('Y-m-d_H-i-s') . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// Iniciar output Excel
echo "\xEF\xBB\xBF"; // BOM para UTF-8
echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
echo '<head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></head>';
echo '<body>';

// Processar relatório baseado no tipo
switch ($tipo_relatorio) {
    case 'colaboradores_completo':
        include 'relatorios/colaboradores_completo.php';
        break;
    case 'colaboradores_vencidos':
        include 'relatorios/colaboradores_vencidos.php';
        break;
    case 'colaboradores_a_vencer':
        include 'relatorios/colaboradores_a_vencer.php';
        break;
    case 'colaboradores_em_andamento':
        include 'relatorios/colaboradores_em_andamento.php';
        break;
    case 'colaboradores_aprovados':
        include 'relatorios/colaboradores_aprovados.php';
        break;
    case 'colaboradores_personalizado':
        include 'relatorios/colaboradores_personalizado.php';
        break;
    case 'cursos_simples':
        include 'relatorios/cursos_simples.php';
        break;
    case 'cursos_completo':
        include 'relatorios/cursos_completo.php';
        break;
    case 'cursos_vencidos':
        include 'relatorios/cursos_vencidos.php';
        break;
    case 'cursos_a_vencer':
        include 'relatorios/cursos_a_vencer.php';
        break;
    case 'cursos_em_andamento':
        include 'relatorios/cursos_em_andamento.php';
        break;
    case 'dashboard_executivo':
        include 'relatorios/dashboard_executivo.php';
        break;
    case 'relatorio_por_pa':
        include 'relatorios/relatorio_por_pa.php';
        break;
    case 'prazos_vencimentos':
        include 'relatorios/prazos_vencimentos.php';
        break;
    case 'performance_trilhas':
        include 'relatorios/performance_trilhas.php';
        break;
    case 'compliance':
        include 'relatorios/compliance.php';
        break;
    case 'personalizado_avancado':
        include 'relatorios/personalizado_avancado.php';
        break;
    default:
        echo '<h1>Tipo de relatório não encontrado</h1>';
        echo '<p>Tipo solicitado: ' . htmlspecialchars($tipo_relatorio) . '</p>';
        break;
}

echo '</body></html>';
?>
