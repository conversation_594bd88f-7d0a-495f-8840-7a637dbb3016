<?php
/**
 * Debug das estatísticas para identificar divergências
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/filtro_usuarios_inativos.php';

echo "<h1>🔍 Debug: Estatísticas Divergentes</h1>\n";

// Limpar cache
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

echo "<h2>1. 🔍 Teste da Função Centralizada</h2>\n";

// Testar função centralizada com debug
$estatisticas_completas = calcularEstatisticasConsistentes([], [], true, true);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resultado da Função Centralizada:</h3>";
echo "<pre>";
print_r($estatisticas_completas);
echo "</pre>";
echo "</div>";

echo "<h2>2. 🔍 Simulação das Páginas</h2>\n";

// Simular página de análise
$estatisticas_analise = [
    'total_colaboradores' => $estatisticas_completas['total_colaboradores'],
    'total_trilhas' => $estatisticas_completas['trilhas_disponiveis'],
    'total_cursos' => $estatisticas_completas['cursos_cadastrados'],
    'total_registros' => $estatisticas_completas['total_registros'],
    'cursos_aprovados' => $estatisticas_completas['cursos_aprovados'],
    'cursos_concluidos' => $estatisticas_completas['cursos_concluidos'],
    'cursos_em_andamento' => $estatisticas_completas['cursos_em_andamento'],
    'cursos_vencidos' => $estatisticas_completas['cursos_vencidos'],
    'media_aproveitamento' => $estatisticas_completas['media_aproveitamento']
];

// Simular página de relatórios
$estatisticas_relatorios = [
    'total_colaboradores' => $estatisticas_completas['total_colaboradores'],
    'total_trilhas' => $estatisticas_completas['total_trilhas_atribuidas'],
    'total_cursos' => $estatisticas_completas['total_cursos_atribuidos'],
    'total_registros' => $estatisticas_completas['total_registros'],
    'cursos_aprovados' => $estatisticas_completas['cursos_aprovados'],
    'media_aproveitamento' => $estatisticas_completas['media_aproveitamento']
];

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Análise de Colaboradores (simulação):</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores:</strong> " . number_format($estatisticas_analise['total_colaboradores']) . "</li>";
echo "<li><strong>Trilhas Disponíveis:</strong> " . number_format($estatisticas_analise['total_trilhas']) . "</li>";
echo "<li><strong>Cursos Cadastrados:</strong> " . number_format($estatisticas_analise['total_cursos']) . "</li>";
echo "<li><strong>Cursos Atribuídos:</strong> " . number_format($estatisticas_completas['total_cursos_atribuidos']) . "</li>";
echo "<li><strong>Cursos Concluídos:</strong> " . number_format($estatisticas_analise['cursos_concluidos']) . "</li>";
echo "<li><strong>Cursos em Andamento:</strong> " . number_format($estatisticas_analise['cursos_em_andamento']) . "</li>";
echo "<li><strong>Cursos Vencidos:</strong> " . number_format($estatisticas_analise['cursos_vencidos']) . "</li>";
echo "<li><strong>Média de Aproveitamento:</strong> " . number_format($estatisticas_analise['media_aproveitamento'], 1) . "%</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Relatórios (simulação):</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores:</strong> " . number_format($estatisticas_relatorios['total_colaboradores']) . "</li>";
echo "<li><strong>Trilhas:</strong> " . number_format($estatisticas_relatorios['total_trilhas']) . "</li>";
echo "<li><strong>Cursos:</strong> " . number_format($estatisticas_relatorios['total_cursos']) . "</li>";
echo "<li><strong>Aprovações:</strong> " . number_format($estatisticas_relatorios['cursos_aprovados']) . "</li>";
echo "</ul>";
echo "</div>";

echo "<h2>3. 🔍 Verificação Manual das Queries</h2>\n";

// Query manual para verificar colaboradores
$query_colaboradores = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos_colaborador
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 5
";

$stmt = $pdo_edu->prepare($query_colaboradores);
$stmt->execute();
$amostra_colaboradores = $stmt->fetchAll();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Amostra de Colaboradores (5 primeiros):</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 8px;'>CPF</th>";
echo "<th style='padding: 8px;'>Nome</th>";
echo "<th style='padding: 8px;'>Cursos</th>";
echo "</tr>";

foreach ($amostra_colaboradores as $colaborador) {
    echo "<tr>";
    echo "<td style='padding: 8px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($colaborador['usuario']) . "</td>";
    echo "<td style='padding: 8px;'>" . $colaborador['total_cursos_colaborador'] . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Aplicar filtro na amostra
$amostra_filtrada = aplicarFiltroUsuariosInativos($amostra_colaboradores, true);

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Amostra Após Filtro:</h3>";
echo "<p><strong>Antes do filtro:</strong> " . count($amostra_colaboradores) . " colaboradores</p>";
echo "<p><strong>Depois do filtro:</strong> " . count($amostra_filtrada) . " colaboradores</p>";
echo "<p><strong>Filtrados:</strong> " . (count($amostra_colaboradores) - count($amostra_filtrada)) . " colaboradores</p>";
echo "</div>";

echo "<h2>4. 🔍 Comparação com Valores Reais das Páginas</h2>\n";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Valores Reportados pelo Usuário:</h3>";

echo "<h4>Análise de Colaboradores:</h4>";
echo "<ul>";
echo "<li>285 Colaboradores</li>";
echo "<li>105 Trilhas Disponíveis</li>";
echo "<li>400 Cursos Cadastrados</li>";
echo "<li>17,748 Cursos Atribuídos</li>";
echo "<li>12,964 Cursos Concluídos</li>";
echo "<li>5,012 Cursos em Andamento</li>";
echo "<li>989 Cursos Vencidos</li>";
echo "<li>82.3% Média de Aproveitamento</li>";
echo "</ul>";

echo "<h4>Relatórios:</h4>";
echo "<ul>";
echo "<li>286 Colaboradores</li>";
echo "<li>3,116 Trilhas</li>";
echo "<li>17,815 Cursos</li>";
echo "<li>12,964 Aprovações</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. 🔍 Análise das Divergências</h2>\n";

$divergencias = [];

// Colaboradores
if ($estatisticas_analise['total_colaboradores'] != 285) {
    $divergencias[] = "Colaboradores: Esperado 285, Calculado " . $estatisticas_analise['total_colaboradores'];
}

// Trilhas
if ($estatisticas_analise['total_trilhas'] != 105) {
    $divergencias[] = "Trilhas Disponíveis: Esperado 105, Calculado " . $estatisticas_analise['total_trilhas'];
}

// Cursos
if ($estatisticas_analise['total_cursos'] != 400) {
    $divergencias[] = "Cursos Cadastrados: Esperado 400, Calculado " . $estatisticas_analise['total_cursos'];
}

echo "<div style='background: " . (empty($divergencias) ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔍 Resultado da Análise:</h3>";

if (empty($divergencias)) {
    echo "<p><strong>✅ Todas as estatísticas estão corretas!</strong></p>";
} else {
    echo "<p><strong>❌ Divergências encontradas:</strong></p>";
    echo "<ul>";
    foreach ($divergencias as $divergencia) {
        echo "<li>$divergencia</li>";
    }
    echo "</ul>";
}
echo "</div>";

echo "<h2>6. 🔗 Links para Teste</h2>\n";

$timestamp = time();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Teste as páginas reais:</h3>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=$timestamp' target='_blank' ";
echo "style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>";
echo "📊 Análise de Colaboradores</a>";
echo "<a href='relatorios.php?nocache=$timestamp' target='_blank' ";
echo "style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>";
echo "📋 Relatórios</a>";
echo "</p>";
echo "</div>";

echo "<p><em>Debug executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
