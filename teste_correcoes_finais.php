<?php
/**
 * Teste para verificar se as correções finais foram aplicadas
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/filtro_usuarios_inativos.php';

echo "<h1>🔧 Teste: Correções Finais</h1>\n";

// Limpar cache
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

echo "<h2>1. 🔍 Teste do Cálculo de Cursos Vencidos</h2>\n";

// Testar função centralizada com métricas detalhadas
$estatisticas_completas = calcularEstatisticasConsistentes([], [], true, true);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Estatísticas Calculadas:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Métrica</th>";
echo "<th style='padding: 10px;'>Valor</th>";
echo "<th style='padding: 10px;'>Status</th>";
echo "</tr>";

$metricas_teste = [
    'total_colaboradores' => 'Colaboradores',
    'total_cursos_atribuidos' => 'Cursos Atribuídos',
    'cursos_concluidos' => 'Cursos Concluídos',
    'cursos_vencidos' => 'Cursos Vencidos',
    'cursos_em_andamento' => 'Cursos em Andamento',
    'cursos_aprovados' => 'Cursos Aprovados'
];

foreach ($metricas_teste as $key => $nome) {
    $valor = $estatisticas_completas[$key];
    $status = '✅ OK';
    
    // Verificações específicas
    if ($key == 'cursos_vencidos' && $valor == 0) {
        $status = '⚠️ Pode estar correto se não há cursos vencidos';
    } elseif ($key == 'cursos_vencidos' && $valor > 0) {
        $status = '✅ Calculado corretamente';
    }
    
    $cor_linha = ($key == 'cursos_vencidos' && $valor > 0) ? '#d4edda' : '#f8f9fa';
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 10px;'><strong>$nome</strong></td>";
    echo "<td style='padding: 10px; text-align: center;'><strong>" . number_format($valor) . "</strong></td>";
    echo "<td style='padding: 10px;'>$status</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<h2>2. 🔍 Verificação Manual de Cursos Vencidos</h2>\n";

// Query manual para verificar cursos vencidos
$query_vencidos = "
    SELECT 
        COUNT(DISTINCT CONCAT(cpf, '|', codigo_trilha, '|', codigo_recurso)) as total_vencidos_manual
    FROM edu_relatorio_educacao 
    WHERE prazo_conclusao < CURDATE() 
    AND (data_conclusao IS NULL OR data_conclusao = '0000-00-00')
";

$stmt_vencidos = $pdo_edu->prepare($query_vencidos);
$stmt_vencidos->execute();
$resultado_manual = $stmt_vencidos->fetch();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔍 Verificação Manual:</h3>";
echo "<ul>";
echo "<li><strong>Cursos vencidos (query manual):</strong> " . number_format($resultado_manual['total_vencidos_manual']) . "</li>";
echo "<li><strong>Cursos vencidos (função centralizada):</strong> " . number_format($estatisticas_completas['cursos_vencidos']) . "</li>";

if ($resultado_manual['total_vencidos_manual'] == $estatisticas_completas['cursos_vencidos']) {
    echo "<li><strong>Status:</strong> ✅ Valores coincidem!</li>";
} else {
    echo "<li><strong>Status:</strong> ⚠️ Valores diferentes - pode ser devido ao filtro de usuários inativos</li>";
}
echo "</ul>";
echo "</div>";

echo "<h2>3. 🔍 Teste de Amostra de Cursos Vencidos</h2>\n";

// Buscar amostra de cursos vencidos
$query_amostra = "
    SELECT 
        cpf,
        usuario,
        trilha,
        recurso,
        prazo_conclusao,
        data_conclusao
    FROM edu_relatorio_educacao 
    WHERE prazo_conclusao < CURDATE() 
    AND (data_conclusao IS NULL OR data_conclusao = '0000-00-00')
    ORDER BY prazo_conclusao DESC
    LIMIT 5
";

$stmt_amostra = $pdo_edu->prepare($query_amostra);
$stmt_amostra->execute();
$amostra_vencidos = $stmt_amostra->fetchAll();

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Amostra de Cursos Vencidos:</h3>";

if (empty($amostra_vencidos)) {
    echo "<p><strong>✅ Nenhum curso vencido encontrado!</strong></p>";
    echo "<p>Isso explica por que o valor é 0.</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 8px;'>CPF</th>";
    echo "<th style='padding: 8px;'>Nome</th>";
    echo "<th style='padding: 8px;'>Curso</th>";
    echo "<th style='padding: 8px;'>Prazo</th>";
    echo "</tr>";
    
    foreach ($amostra_vencidos as $curso) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . substr($curso['cpf'], 0, 3) . "***</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['usuario']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['recurso']) . "</td>";
        echo "<td style='padding: 8px;'>" . date('d/m/Y', strtotime($curso['prazo_conclusao'])) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<p><strong>Total encontrado:</strong> " . count($amostra_vencidos) . " cursos (amostra de 5)</p>";
}
echo "</div>";

echo "<h2>4. 🔗 Teste das Páginas</h2>\n";

$timestamp = time();

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Teste as páginas agora:</h3>";
echo "<p><strong>Verifique se:</strong></p>";
echo "<ul>";
echo "<li>✅ Cursos vencidos não está mais como 0 (se houver cursos vencidos)</li>";
echo "<li>✅ Quantidade de colaboradores nas estatísticas = quantidade na seção de colaboradores</li>";
echo "<li>✅ Filtro de usuários inativos aplicado em toda a página</li>";
echo "</ul>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=$timestamp' target='_blank' ";
echo "style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>";
echo "📊 Análise de Colaboradores</a>";
echo "<a href='relatorios.php?nocache=$timestamp' target='_blank' ";
echo "style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>";
echo "📋 Relatórios</a>";
echo "</p>";
echo "</div>";

echo "<h2>5. 🎯 Resultado Esperado</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Após as correções:</h3>";
echo "<ul>";
echo "<li><strong>Cursos Vencidos:</strong> " . number_format($estatisticas_completas['cursos_vencidos']) . " (não mais 0, se houver cursos vencidos)</li>";
echo "<li><strong>Colaboradores (estatísticas):</strong> " . number_format($estatisticas_completas['total_colaboradores']) . "</li>";
echo "<li><strong>Colaboradores (seção):</strong> Deve ser igual às estatísticas</li>";
echo "<li><strong>Filtro aplicado:</strong> Em todas as seções da página</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Correções Aplicadas:</h3>";
echo "<ol>";
echo "<li><strong>Cálculo de Cursos Vencidos:</strong> Agora usa query com data de prazo vs data atual</li>";
echo "<li><strong>Filtro na Seção de Colaboradores:</strong> Aplicado após buscar colaboradores</li>";
echo "<li><strong>Consistência:</strong> Mesma base de dados em estatísticas e seção</li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
