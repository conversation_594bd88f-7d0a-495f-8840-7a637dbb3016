<?php
/**
 * Teste para verificar se o filtro de usuários inativos está sendo aplicado em todas as páginas
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/filtro_usuarios_inativos.php';

echo "<h1>🧪 Teste: Filtro Global de Usuários Inativos</h1>\n";

// Limpar cache
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// Buscar colaboradores específicos para teste
$colaboradores_teste = [
    'ALICE BEATRIZ DA SILVA',
    'CLESIO GOMES DE JESUS'
];

echo "<h2>1. 🔍 Verificar Colaboradores de Teste</h2>\n";

$colaboradores_encontrados = [];
foreach ($colaboradores_teste as $nome) {
    $query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE ? GROUP BY cpf LIMIT 1";
    $stmt = $pdo_edu->prepare($query);
    $stmt->execute(["%$nome%"]);
    $resultado = $stmt->fetch();
    
    if ($resultado) {
        $colaboradores_encontrados[] = $resultado;
        echo "<div style='background: #fff3cd; padding: 5px; margin: 5px 0;'>";
        echo "<p>✅ <strong>$nome:</strong> CPF " . substr($resultado['cpf'], 0, 3) . "*** encontrado</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 5px; margin: 5px 0;'>";
        echo "<p>❌ <strong>$nome:</strong> Não encontrado no banco</p>";
        echo "</div>";
    }
}

echo "<h2>2. 🧪 Teste da Função de Filtro</h2>\n";

if (!empty($colaboradores_encontrados)) {
    // Testar função de filtro
    $colaboradores_antes = $colaboradores_encontrados;
    $colaboradores_depois = aplicarFiltroUsuariosInativos($colaboradores_encontrados, true);
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px;'>Colaborador</th>";
    echo "<th style='padding: 10px;'>CPF</th>";
    echo "<th style='padding: 10px;'>Antes do Filtro</th>";
    echo "<th style='padding: 10px;'>Depois do Filtro</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "</tr>";
    
    foreach ($colaboradores_antes as $colaborador) {
        $encontrado_depois = false;
        foreach ($colaboradores_depois as $col_depois) {
            if ($col_depois['cpf'] === $colaborador['cpf']) {
                $encontrado_depois = true;
                break;
            }
        }
        
        $status = $encontrado_depois ? '✅ MANTIDO' : '❌ FILTRADO';
        $cor_linha = $encontrado_depois ? '#d4edda' : '#f8d7da';
        
        echo "<tr style='background-color: $cor_linha;'>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($colaborador['usuario']) . "</td>";
        echo "<td style='padding: 10px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
        echo "<td style='padding: 10px;'>✅ PRESENTE</td>";
        echo "<td style='padding: 10px;'>" . ($encontrado_depois ? '✅ PRESENTE' : '❌ REMOVIDO') . "</td>";
        echo "<td style='padding: 10px;'><strong>$status</strong></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Resultado do Filtro:</h3>";
    echo "<ul>";
    echo "<li><strong>Colaboradores antes:</strong> " . count($colaboradores_antes) . "</li>";
    echo "<li><strong>Colaboradores depois:</strong> " . count($colaboradores_depois) . "</li>";
    echo "<li><strong>Filtrados:</strong> " . (count($colaboradores_antes) - count($colaboradores_depois)) . "</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>3. 🔗 Teste das Páginas Principais</h2>\n";

$paginas_teste = [
    'analise_colaboradores.php?aba=colaboradores' => 'Análise de Colaboradores',
    'relatorios.php' => 'Relatórios',
    'emails.php' => 'Gerenciamento de E-mails',
    'ajax_colaboradores.php?search=clesio&limit=10' => 'AJAX Colaboradores (API)',
    'detalhes_colaborador.php?cpf=' . ($colaboradores_encontrados[0]['cpf'] ?? '00000000000') => 'Detalhes do Colaborador'
];

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Links para Teste Manual:</h3>";
echo "<p><strong>Teste cada página para verificar se o filtro está funcionando:</strong></p>";

foreach ($paginas_teste as $url => $nome) {
    $url_completa = $url . (strpos($url, '?') !== false ? '&' : '?') . 'nocache=' . time();
    echo "<p>";
    echo "<a href='$url_completa' target='_blank' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>";
    echo "🔍 $nome</a>";
    echo "</p>";
}
echo "</div>";

echo "<h2>4. 🎯 Verificações Específicas</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ O que verificar em cada página:</h3>";
echo "<ul>";
echo "<li><strong>Análise de Colaboradores:</strong> ALICE e CLESIO não devem aparecer</li>";
echo "<li><strong>Relatórios:</strong> Contagem total deve excluir inativos</li>";
echo "<li><strong>E-mails:</strong> Lista de colaboradores deve excluir inativos</li>";
echo "<li><strong>AJAX Colaboradores:</strong> Busca por 'clesio' não deve retornar resultados</li>";
echo "<li><strong>Detalhes do Colaborador:</strong> Deve funcionar normalmente para ativos</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. 🧪 Teste de Filtros Específicos</h2>\n";

// Teste específico para filtro por nome
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔍 Teste de Filtros por Nome:</h3>";

$filtros_teste = ['alice', 'clesio', 'beatriz', 'gomes'];

foreach ($filtros_teste as $filtro) {
    $url_filtro = "analise_colaboradores.php?aba=colaboradores&nome=$filtro&nocache=" . time();
    echo "<p>";
    echo "<a href='$url_filtro' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin-right: 5px;'>";
    echo "Filtrar por '$filtro'</a>";
    echo " - <em>Deve retornar 0 colaboradores se '$filtro' for inativo</em>";
    echo "</p>";
}
echo "</div>";

echo "<h2>6. 📋 Checklist de Verificação</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Checklist Final:</h3>";
echo "<ol>";
echo "<li>☐ <strong>Página Principal:</strong> ALICE e CLESIO não aparecem</li>";
echo "<li>☐ <strong>Contagem Total:</strong> Número correto (sem inativos)</li>";
echo "<li>☐ <strong>Filtros por Nome:</strong> Não retornam inativos</li>";
echo "<li>☐ <strong>Relatórios:</strong> Excluem colaboradores inativos</li>";
echo "<li>☐ <strong>E-mails:</strong> Lista apenas colaboradores ativos</li>";
echo "<li>☐ <strong>AJAX:</strong> Busca não retorna inativos</li>";
echo "<li>☐ <strong>Detalhes:</strong> Funciona para colaboradores ativos</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚠️ Se algum teste falhar:</h3>";
echo "<ul>";
echo "<li>Verifique se o arquivo <code>includes/filtro_usuarios_inativos.php</code> foi incluído</li>";
echo "<li>Verifique se a função <code>aplicarFiltroUsuariosInativos()</code> está sendo chamada</li>";
echo "<li>Limpe o cache do navegador</li>";
echo "<li>Verifique logs de erro do PHP</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Resultado Esperado:</h3>";
echo "<p><strong>Após implementar o filtro global:</strong></p>";
echo "<ul>";
echo "<li>✅ Todas as páginas devem excluir usuários inativos da Intranet</li>";
echo "<li>✅ Contagens e estatísticas devem refletir apenas usuários ativos + não encontrados</li>";
echo "<li>✅ Filtros e buscas devem funcionar apenas com usuários válidos</li>";
echo "<li>✅ Relatórios devem incluir apenas colaboradores relevantes</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
