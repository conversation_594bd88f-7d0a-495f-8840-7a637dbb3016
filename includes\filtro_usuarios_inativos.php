<?php
/**
 * Função utilitária para aplicar filtro de usuários inativos da Intranet
 * Deve ser incluída em todas as páginas que fazem consultas de colaboradores
 */

require_once __DIR__ . '/../classes/IntranetAPI.php';

/**
 * Aplica filtro de usuários inativos da Intranet em uma lista de colaboradores
 * 
 * @param array $colaboradores Lista de colaboradores do banco de dados
 * @param bool $force_no_cache Forçar busca sem cache da API
 * @return array Lista filtrada (sem usuários inativos da Intranet)
 */
function aplicarFiltroUsuariosInativos($colaboradores, $force_no_cache = false) {
    // Cache para evitar múltiplas chamadas à API na mesma requisição
    static $mapa_usuarios_ativos = null;
    static $mapa_usuarios_todos = null;
    static $cache_inicializado = false;
    
    // Inicializar cache se necessário
    if (!$cache_inicializado || $force_no_cache) {
        $api = new IntranetAPI();
        
        // Buscar dados da API
        $usuarios_intranet_todos = $api->listarUsuarios(false, $force_no_cache);
        $usuarios_intranet_ativos = $api->listarUsuariosAtivos($force_no_cache);
        
        // Criar mapas
        $mapa_usuarios_ativos = [];
        $mapa_usuarios_todos = [];
        
        if (is_array($usuarios_intranet_ativos)) {
            foreach ($usuarios_intranet_ativos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        if (is_array($usuarios_intranet_todos)) {
            foreach ($usuarios_intranet_todos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        $cache_inicializado = true;
    }
    
    // Se não conseguiu buscar dados da API, retornar lista original
    if ($mapa_usuarios_ativos === null || $mapa_usuarios_todos === null) {
        return $colaboradores;
    }
    
    // Aplicar filtro
    $colaboradores_filtrados = [];
    $total_filtrados = 0;
    
    foreach ($colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        
        // Verificar status na Intranet
        $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
        
        // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
        if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
            $total_filtrados++;
            continue; // Usuário inativo - PULAR
        }
        
        // Incluir colaborador na lista filtrada
        $colaboradores_filtrados[] = $colaborador;
    }
    
    return $colaboradores_filtrados;
}

/**
 * Obtém mapas de usuários da Intranet para uso em outras funções
 * 
 * @param bool $force_no_cache Forçar busca sem cache da API
 * @return array Array com 'ativos' e 'todos' contendo os mapas de usuários
 */
function obterMapasUsuariosIntranet($force_no_cache = false) {
    static $mapas_cache = null;
    static $cache_inicializado = false;
    
    if (!$cache_inicializado || $force_no_cache) {
        $api = new IntranetAPI();
        
        // Buscar dados da API
        $usuarios_intranet_todos = $api->listarUsuarios(false, $force_no_cache);
        $usuarios_intranet_ativos = $api->listarUsuariosAtivos($force_no_cache);
        
        // Criar mapas
        $mapa_usuarios_ativos = [];
        $mapa_usuarios_todos = [];
        
        if (is_array($usuarios_intranet_ativos)) {
            foreach ($usuarios_intranet_ativos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        if (is_array($usuarios_intranet_todos)) {
            foreach ($usuarios_intranet_todos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        $mapas_cache = [
            'ativos' => $mapa_usuarios_ativos,
            'todos' => $mapa_usuarios_todos
        ];
        
        $cache_inicializado = true;
    }
    
    return $mapas_cache;
}

/**
 * Adiciona condições WHERE para filtrar apenas usuários ativos da Intranet
 * 
 * @param array $where_conditions Array de condições WHERE existentes
 * @param array $params Array de parâmetros existentes
 * @param bool $force_no_cache Forçar busca sem cache da API
 * @return array Array com 'where_conditions' e 'params' atualizados
 */
function adicionarFiltroUsuariosAtivosSQL($where_conditions, $params, $force_no_cache = false) {
    $mapas = obterMapasUsuariosIntranet($force_no_cache);
    
    if (empty($mapas['ativos']) && empty($mapas['todos'])) {
        // Se não conseguiu buscar dados da API, não aplicar filtro
        return ['where_conditions' => $where_conditions, 'params' => $params];
    }
    
    // Criar lista de CPFs que devem ser incluídos
    $cpfs_incluir = [];
    
    // Incluir todos os CPFs ativos
    $cpfs_incluir = array_merge($cpfs_incluir, array_keys($mapas['ativos']));
    
    // Incluir CPFs que não existem na Intranet (serão mostrados como "Sem PA")
    // Para isso, precisamos fazer uma consulta para pegar todos os CPFs únicos do banco
    // e depois filtrar apenas os que NÃO estão no mapa de todos os usuários
    
    if (!empty($cpfs_incluir)) {
        // Adicionar condição para incluir apenas CPFs ativos ou não encontrados na Intranet
        $cpfs_placeholders = str_repeat('?,', count($cpfs_incluir) - 1) . '?';
        
        // Condição: CPF está na lista de ativos OU não está na lista de todos os usuários da Intranet
        $cpfs_todos_intranet = array_keys($mapas['todos']);
        
        if (!empty($cpfs_todos_intranet)) {
            $cpfs_todos_placeholders = str_repeat('?,', count($cpfs_todos_intranet) - 1) . '?';
            $where_conditions[] = "(cpf IN ($cpfs_placeholders) OR cpf NOT IN ($cpfs_todos_placeholders))";
            $params = array_merge($params, $cpfs_incluir, $cpfs_todos_intranet);
        } else {
            $where_conditions[] = "cpf IN ($cpfs_placeholders)";
            $params = array_merge($params, $cpfs_incluir);
        }
    }
    
    return ['where_conditions' => $where_conditions, 'params' => $params];
}

/**
 * Verifica se um CPF deve ser filtrado (é inativo na Intranet)
 *
 * @param string $cpf CPF do colaborador
 * @param bool $force_no_cache Forçar busca sem cache da API
 * @return bool True se deve ser filtrado (inativo), False se deve ser incluído
 */
function deveSerFiltrado($cpf, $force_no_cache = false) {
    $mapas = obterMapasUsuariosIntranet($force_no_cache);

    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);

    $usuario_intranet_ativo = $mapas['ativos'][$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapas['todos'][$cpf_normalizado] ?? null;

    // Filtrar se existe na Intranet mas está inativo
    return ($usuario_intranet_todos && !$usuario_intranet_ativo);
}

/**
 * Calcula estatísticas consistentes aplicando filtro de usuários inativos
 *
 * @param array $where_conditions Condições WHERE adicionais
 * @param array $params Parâmetros para as condições WHERE
 * @param bool $force_no_cache Forçar busca sem cache da API
 * @param bool $incluir_metricas_detalhadas Incluir cálculos de cursos em andamento e vencidos
 * @return array Estatísticas calculadas
 */
function calcularEstatisticasConsistentes($where_conditions = [], $params = [], $force_no_cache = false, $incluir_metricas_detalhadas = false) {
    global $pdo_edu;

    // Query para buscar dados básicos de colaboradores
    $colaboradores_query = "
        SELECT
            cpf,
            MAX(usuario) as usuario,
            COUNT(DISTINCT trilha) as total_trilhas_colaborador,
            COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos_colaborador,
            COUNT(*) as total_registros_colaborador,
            COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_aprovados_colaborador,
            COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos_colaborador,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento_colaborador
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
    ";

    $stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
    $stmt_colaboradores->execute($params);
    $todos_colaboradores = $stmt_colaboradores->fetchAll();

    // Aplicar filtro de usuários inativos
    $colaboradores_filtrados = aplicarFiltroUsuariosInativos($todos_colaboradores, $force_no_cache);

    // Calcular estatísticas agregadas
    $total_colaboradores = count($colaboradores_filtrados);
    $total_trilhas = 0;
    $total_cursos = 0;
    $total_registros = 0;
    $total_aprovados = 0;
    $total_concluidos = 0;
    $soma_medias = 0;
    $count_medias = 0;

    foreach ($colaboradores_filtrados as $colaborador) {
        $total_trilhas += $colaborador['total_trilhas_colaborador'];
        $total_cursos += $colaborador['total_cursos_colaborador'];
        $total_registros += $colaborador['total_registros_colaborador'];
        $total_aprovados += $colaborador['cursos_aprovados_colaborador'];
        $total_concluidos += $colaborador['cursos_concluidos_colaborador'];

        if ($colaborador['media_aproveitamento_colaborador'] > 0) {
            $soma_medias += $colaborador['media_aproveitamento_colaborador'];
            $count_medias++;
        }
    }

    // Buscar estatísticas globais (trilhas e cursos únicos no sistema)
    $globais_query = "
        SELECT
            COUNT(DISTINCT trilha) as trilhas_disponiveis,
            COUNT(DISTINCT recurso) as cursos_cadastrados
        FROM edu_relatorio_educacao
    ";
    $stmt_globais = $pdo_edu->prepare($globais_query);
    $stmt_globais->execute();
    $globais = $stmt_globais->fetch();

    // Calcular métricas adicionais se solicitado
    $cursos_em_andamento = 0;
    $cursos_vencidos = 0;

    if ($incluir_metricas_detalhadas) {
        // Estimar cursos em andamento (atribuídos - concluídos)
        $cursos_em_andamento = max(0, $total_cursos - $total_concluidos);

        // Para cursos vencidos, seria necessário calcular com base em datas
        // Por enquanto, usar uma estimativa baseada na diferença entre aprovados e concluídos
        $cursos_vencidos = max(0, $total_aprovados - $total_concluidos);
    }

    return [
        'total_colaboradores' => $total_colaboradores,
        'trilhas_disponiveis' => $globais['trilhas_disponiveis'],
        'cursos_cadastrados' => $globais['cursos_cadastrados'],
        'total_trilhas_atribuidas' => $total_trilhas,
        'total_cursos_atribuidos' => $total_cursos,
        'total_registros' => $total_registros,
        'cursos_aprovados' => $total_aprovados,
        'cursos_concluidos' => $total_concluidos,
        'cursos_em_andamento' => $cursos_em_andamento,
        'cursos_vencidos' => $cursos_vencidos,
        'media_aproveitamento' => $count_medias > 0 ? ($soma_medias / $count_medias) : 0,
        'colaboradores_dados' => $colaboradores_filtrados
    ];
}
?>
