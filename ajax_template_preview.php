<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/EmailManager.php';
require_once 'classes/IntranetAPI.php';

header('Content-Type: application/json');

try {
    $template_id = $_GET['template_id'] ?? null;
    $cpf = $_GET['cpf'] ?? null;
    
    if (!$template_id || !$cpf) {
        throw new Exception("Template ID e CPF são obrigatórios");
    }
    
    // Buscar template
    $emailManager = new EmailManager($pdo_edu);
    $template = $emailManager->buscarTemplate($template_id);
    
    if (!$template) {
        throw new Exception("Template não encontrado");
    }
    
    // Buscar dados do colaborador
    $cpf = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);
    
    $stmt = $pdo_edu->prepare("
        SELECT
            cpf,
            usuario,
            email,
            funcao,
            codigo_unidade,
            hierarquia_unidade,
            data_admissao,
            superior_imediato,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            SUM(CASE WHEN situacao_trilha = 'Concluída' THEN 1 ELSE 0 END) as trilhas_concluidas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        GROUP BY cpf, usuario, email, funcao, codigo_unidade, hierarquia_unidade, data_admissao, superior_imediato
    ");
    $stmt->execute([$cpf]);
    $colaborador = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$colaborador) {
        throw new Exception("Colaborador não encontrado");
    }
    
    // Buscar dados da Intranet
    $api = new IntranetAPI();
    $usuario_intranet = null;
    $usuarios_intranet = $api->listarUsuarios();
    if ($usuarios_intranet !== false) {
        foreach ($usuarios_intranet as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_intranet = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                if ($cpf_intranet === $cpf) {
                    $usuario_intranet = $usuario;
                    break;
                }
            }
        }
    }
    
    // Buscar agências da Intranet
    $agencias_intranet = $api->listarAgencias();
    $mapa_agencias = [];
    if ($agencias_intranet !== false) {
        foreach ($agencias_intranet as $agencia) {
            if (!empty($agencia['id'])) {
                $mapa_agencias[$agencia['id']] = $agencia;
            }
        }
    }
    
    // Preparar dados do colaborador para processamento
    $dados_colaborador = [
        'cpf' => $colaborador['cpf'],
        'nome' => $usuario_intranet['nome'] ?? $colaborador['usuario'],
        'email' => $usuario_intranet['email'] ?? $colaborador['email'],
        'funcao' => $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'],
        'pa' => $colaborador['codigo_unidade'],
        'total_cursos' => $colaborador['total_cursos'],
        'cursos_aprovados' => $colaborador['cursos_aprovados'],
        'cursos_a_vencer' => 0, // Será calculado
        'cursos_vencidos' => 0  // Será calculado
    ];
    
    // Buscar informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        }
    }
    $dados_colaborador['pa'] = $agencia_info;
    
    // Calcular cursos a vencer e vencidos (simplificado para preview)
    $stmt_cursos = $pdo_edu->prepare("
        SELECT
            COUNT(CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) 
                       AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 1 END) as cursos_a_vencer,
            COUNT(CASE WHEN concluir_trilha_ate < CURDATE() 
                       AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 1 END) as cursos_vencidos
        FROM edu_relatorio_educacao
        WHERE cpf = ?
    ");
    $stmt_cursos->execute([$cpf]);
    $metricas_cursos = $stmt_cursos->fetch(PDO::FETCH_ASSOC);
    
    $dados_colaborador['cursos_a_vencer'] = $metricas_cursos['cursos_a_vencer'] ?? 0;
    $dados_colaborador['cursos_vencidos'] = $metricas_cursos['cursos_vencidos'] ?? 0;
    
    // Processar template com dados do colaborador
    $conteudo = $emailManager->processarVariaveis($template, $dados_colaborador);
    
    echo json_encode([
        'success' => true,
        'preview' => [
            'assunto' => $conteudo['assunto'],
            'corpo_html' => $conteudo['corpo_html']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
