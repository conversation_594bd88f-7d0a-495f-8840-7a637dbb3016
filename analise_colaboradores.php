<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar permissões (todos os níveis podem acessar)
checkPageAccess(['comum', 'gestor', 'admin']);

// Inicializar API da Intranet
$api = new IntranetAPI();

// Sempre exibir análise por colaboradores (abas removidas)
$aba_ativa = 'colaboradores';
$filtros = [
    'cpf' => $_GET['cpf'] ?? '',
    'nome' => $_GET['nome'] ?? '',
    'trilha' => $_GET['trilha'] ?? '',
    'trilha_obrigatoria' => $_GET['trilha_obrigatoria'] ?? '',
    'situacao_trilha' => $_GET['situacao_trilha'] ?? '',
    'agencia' => $_GET['agencia'] ?? '',
    'pa' => $_GET['pa'] ?? '',
    'status_curso' => [],
    'curso' => $_GET['curso'] ?? '',
    'aprovacao' => $_GET['aprovacao'] ?? '',
    'periodo_prazo' => $_GET['periodo_prazo'] ?? '',
    'page' => max(1, intval($_GET['page'] ?? 1)),
    'aba' => $aba_ativa
];

// Processar filtro de status múltiplo
if (!empty($_GET['status_curso'])) {
    if (is_array($_GET['status_curso'])) {
        $filtros['status_curso'] = array_filter($_GET['status_curso']);
    } else {
        // Compatibilidade com filtro único (string)
        $filtros['status_curso'] = [$_GET['status_curso']];
    }
}

// OTIMIZAÇÃO: Cache para dados da API da Intranet
$cache_key = 'analise_colaboradores_api_data';
$cache_file = EDU_API_CACHE_PATH . $cache_key . '.json';
$cache_time = EDU_API_CACHE_TIME;

$api_data_cached = false;
if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_time) {
    $cached_data = json_decode(file_get_contents($cache_file), true);
    if ($cached_data && isset($cached_data['usuarios']) && isset($cached_data['agencias'])) {
        $usuarios_intranet = $cached_data['usuarios'];
        $agencias_intranet = $cached_data['agencias'];
        $api_data_cached = true;
    }
}

if (!$api_data_cached) {
    // Buscar dados da API da Intranet (com cache)
    $usuarios_intranet = $api->listarUsuarios();
    $agencias_intranet = $api->listarAgencias();

    // Salvar no cache
    if ($usuarios_intranet !== false && $agencias_intranet !== false) {
        $cache_data = [
            'usuarios' => $usuarios_intranet,
            'agencias' => $agencias_intranet,
            'timestamp' => time()
        ];
        file_put_contents($cache_file, json_encode($cache_data));
    }
}

// OTIMIZAÇÃO: Criar mapas otimizados de usuários e agências
$mapa_usuarios_cpf = [];
$mapa_agencias = [];

if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            // Normalizar CPF (remover pontos e traços, completar com zeros)
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
}

if ($agencias_intranet !== false) {
    foreach ($agencias_intranet as $agencia) {
        if (!empty($agencia['id'])) {
            $mapa_agencias[$agencia['id']] = $agencia;
        }
    }
}

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['cpf'])) {
    $cpf_filtro = str_pad(preg_replace('/[^0-9]/', '', $filtros['cpf']), 11, '0', STR_PAD_LEFT);
    $where_conditions[] = "cpf = ?";
    $params[] = $cpf_filtro;
}

if (!empty($filtros['nome'])) {
    $where_conditions[] = "usuario LIKE ?";
    $params[] = '%' . $filtros['nome'] . '%';
}

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

// Buscar trilhas obrigatórias primeiro (com verificação se a tabela existe)
$trilhas_obrigatorias = [];
try {
    $trilhas_obrigatorias_query = "SELECT trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1 ORDER BY trilha";
    $trilhas_obrigatorias = $pdo_edu->query($trilhas_obrigatorias_query)->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // Tabela ainda não existe, continuar sem filtro de trilhas obrigatórias
    $trilhas_obrigatorias = [];
}

// Filtro por trilhas obrigatórias (apenas se a tabela existir)
if (!empty($filtros['trilha_obrigatoria']) && count($trilhas_obrigatorias) > 0) {
    if ($filtros['trilha_obrigatoria'] === 'sim') {
        $where_conditions[] = "trilha IN (SELECT trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1)";
    } elseif ($filtros['trilha_obrigatoria'] === 'nao') {
        $where_conditions[] = "(trilha NOT IN (SELECT trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1) OR trilha NOT IN (SELECT trilha FROM edu_trilhas_obrigatorias))";
    }
}

// Filtro por status do curso será aplicado após buscar os dados
// pois depende do cálculo de prazos personalizados





// OTIMIZAÇÃO: Query otimizada para estatísticas gerais com cache
$stats_cache_key = 'stats_' . md5(serialize($where_conditions) . serialize($params));
$stats_cache_file = EDU_API_CACHE_PATH . $stats_cache_key . '.json';

$estatisticas = null;
if (file_exists($stats_cache_file) && (time() - filemtime($stats_cache_file)) < 300) { // Cache de 5 minutos
    $estatisticas = json_decode(file_get_contents($stats_cache_file), true);
}

if (!$estatisticas) {
    $stats_query = "
        SELECT
            COUNT(DISTINCT cpf) as total_colaboradores,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            COUNT(*) as total_registros,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "");

    $stmt_stats = $pdo_edu->prepare($stats_query);
    $stmt_stats->execute($params);
    $estatisticas = $stmt_stats->fetch();

    // Salvar no cache
    file_put_contents($stats_cache_file, json_encode($estatisticas));
}

// Query para colaboradores únicos agrupados por PA (apenas se não for aba de cursos)
$colaboradores_por_pa = [];
$total_colaboradores = 0;
$total_pages = 0;
$total_pas = 0;

if ($aba_ativa === 'colaboradores') {
    // OTIMIZAÇÃO: Cache para colaboradores com chave baseada nos filtros
    $colaboradores_cache_key = 'colaboradores_' . md5(serialize($where_conditions) . serialize($params));
    $colaboradores_cache_file = EDU_API_CACHE_PATH . $colaboradores_cache_key . '.json';

    $todos_colaboradores = null;
    if (file_exists($colaboradores_cache_file) && (time() - filemtime($colaboradores_cache_file)) < 600) { // Cache de 10 minutos
        $todos_colaboradores = json_decode(file_get_contents($colaboradores_cache_file), true);
    }

    if (!$todos_colaboradores) {
        // OTIMIZAÇÃO: Query otimizada com índices compostos
        $colaboradores_query = "
            SELECT
                cpf,
                MAX(usuario) as usuario,
                MAX(email) as email,
                MAX(funcao) as funcao,
                MAX(codigo_unidade) as codigo_unidade,
                MAX(data_admissao) as data_admissao,
                COUNT(DISTINCT trilha) as total_trilhas,
                COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
                COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_aprovados,
                COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos,
                0 as cursos_a_vencer,
                0 as cursos_vencidos,
                AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
                MAX(data_importacao) as ultima_atualizacao
            FROM edu_relatorio_educacao
            " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
            GROUP BY cpf
            ORDER BY MAX(usuario)";

        $stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
        $stmt_colaboradores->execute($params);
        $todos_colaboradores = $stmt_colaboradores->fetchAll();

        // Salvar no cache
        file_put_contents($colaboradores_cache_file, json_encode($todos_colaboradores));
    }





    // OTIMIZAÇÃO: Cache para configurações de prazos personalizados
    $prazos_cache_key = 'prazos_personalizados';
    $prazos_cache_file = EDU_API_CACHE_PATH . $prazos_cache_key . '.json';

    $prazos_config = [];
    if (file_exists($prazos_cache_file) && (time() - filemtime($prazos_cache_file)) < 1800) { // Cache de 30 minutos
        $prazos_config = json_decode(file_get_contents($prazos_cache_file), true);
    } else {
        $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
        foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
            $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
            $prazos_config[$key] = $config;
        }
        file_put_contents($prazos_cache_file, json_encode($prazos_config));
    }

    // OTIMIZAÇÃO: Calcular cursos vencidos e a vencer em lote usando SQL otimizada
    $cpfs_list = array_column($todos_colaboradores, 'cpf');
    $cpfs_placeholders = str_repeat('?,', count($cpfs_list) - 1) . '?';

    // Query otimizada para buscar todos os cursos de uma vez
    $cursos_batch_query = "
        SELECT
            cpf,
            codigo_trilha,
            codigo_recurso,
            aprovacao,
            data_conclusao,
            concluir_trilha_ate,
            data_admissao,
            andamento_etapa,
            CASE
                WHEN concluir_trilha_ate < CURDATE() AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'vencido'
                WHEN concluir_trilha_ate >= CURDATE() AND concluir_trilha_ate <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'a_vencer'
                ELSE 'em_dia'
            END as status_prazo_basico
        FROM edu_relatorio_educacao
        WHERE cpf IN ($cpfs_placeholders)
        ORDER BY cpf, trilha, recurso
    ";

    $stmt_cursos_batch = $pdo_edu->prepare($cursos_batch_query);
    $stmt_cursos_batch->execute($cpfs_list);
    $todos_cursos = $stmt_cursos_batch->fetchAll(PDO::FETCH_ASSOC);

    // Agrupar cursos por CPF para processamento eficiente
    $cursos_por_cpf = [];
    foreach ($todos_cursos as $curso) {
        $cursos_por_cpf[$curso['cpf']][] = $curso;
    }

    // Calcular métricas de forma otimizada
    foreach ($todos_colaboradores as &$colaborador) {
        $cpf = $colaborador['cpf'];
        $cursos_colaborador = $cursos_por_cpf[$cpf] ?? [];

        $vencidos = 0;
        $a_vencer = 0;

        foreach ($cursos_colaborador as $curso) {
            // Aplicar lógica de prazos personalizados se necessário
            $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);

            if ($status_final === 'vencido') {
                $vencidos++;
            } elseif ($status_final === 'a_vencer') {
                $a_vencer++;
            }
        }

        $colaborador['cursos_vencidos'] = $vencidos;
        $colaborador['cursos_a_vencer'] = $a_vencer;
    }

    // OTIMIZAÇÃO: Aplicar filtros de forma mais eficiente usando dados já carregados
    if (!empty($filtros['status_curso']) || !empty($filtros['periodo_prazo'])) {
        $colaboradores_filtrados = [];

        foreach ($todos_colaboradores as $colaborador) {
            $cpf = $colaborador['cpf'];
            $cursos_colaborador = $cursos_por_cpf[$cpf] ?? [];

            $atende_filtro_status = true;
            $atende_filtro_periodo = true;

            // Verificar filtro por status (se aplicável)
            if (!empty($filtros['status_curso'])) {
                $status_encontrados = [];

                foreach ($cursos_colaborador as $curso) {
                    $status_curso = '';

                    // Determinar status do curso de forma otimizada
                    if ($curso['aprovacao'] === 'Sim') {
                        $status_curso = 'aprovado';
                    } else {
                        $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);
                        if ($status_final === 'vencido') {
                            $status_curso = 'vencido';
                        } elseif ($status_final === 'a_vencer') {
                            $status_curso = 'a_vencer';
                        } elseif (!empty($curso['andamento_etapa'])) {
                            $status_curso = 'em_andamento';
                        }
                    }

                    // Marcar status encontrado se estiver na lista de filtros
                    if (in_array($status_curso, $filtros['status_curso'])) {
                        $status_encontrados[$status_curso] = true;
                    }
                }

                // Colaborador deve ter cursos com TODOS os status selecionados
                foreach ($filtros['status_curso'] as $status_requerido) {
                    if (!isset($status_encontrados[$status_requerido])) {
                        $atende_filtro_status = false;
                        break;
                    }
                }
            }

            // Verificar filtro por período (se aplicável) - simplificado
            if (!empty($filtros['periodo_prazo']) && $atende_filtro_status) {
                $tem_curso_no_periodo = false;

                foreach ($cursos_colaborador as $curso) {
                    if (!empty($curso['concluir_trilha_ate'])) {
                        $prazo_curso = new DateTime($curso['concluir_trilha_ate']);
                        if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo_prazo'])) {
                            $tem_curso_no_periodo = true;
                            break;
                        }
                    }
                }

                $atende_filtro_periodo = $tem_curso_no_periodo;
            }

            // Incluir colaborador se atende a todos os filtros aplicados
            if ($atende_filtro_status && $atende_filtro_periodo) {
                $colaboradores_filtrados[] = $colaborador;
            }
        }

        $todos_colaboradores = $colaboradores_filtrados;
    }

    // Reagrupar colaboradores por PA APÓS calcular as métricas corretas
    $colaboradores_por_pa = [];
    $cpfs_processados = []; // Controle para evitar duplicatas

    foreach ($todos_colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);

        // Verificar se já processamos este CPF
        if (isset($cpfs_processados[$cpf_normalizado])) {
            continue; // Pular colaborador duplicado
        }
        $cpfs_processados[$cpf_normalizado] = true;

        $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

        // Determinar PA
        $pa_info = ['id' => 'sem_pa', 'numero' => 'S/PA', 'nome' => 'Sem PA Definido'];

        if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
            $agencia_id = $usuario_intranet['agencia'];
            if (isset($mapa_agencias[$agencia_id])) {
                $agencia_data = $mapa_agencias[$agencia_id];
                $pa_info = [
                    'id' => $agencia_id,
                    'numero' => $agencia_data['numero'],
                    'nome' => $agencia_data['nome']
                ];
            } else {
                $pa_info = ['id' => $agencia_id, 'numero' => $agencia_id, 'nome' => 'PA ' . $agencia_id];
            }
        }

        $pa_key = $pa_info['numero'] . ' - ' . $pa_info['nome'];

        if (!isset($colaboradores_por_pa[$pa_key])) {
            $colaboradores_por_pa[$pa_key] = [
                'info' => $pa_info,
                'colaboradores' => [],
                'colaboradores_cpfs' => [], // Controle adicional de CPFs únicos
                'total_colaboradores' => 0,
                'total_cursos' => 0,
                'total_concluidos' => 0,
                'total_vencidos' => 0,
                'total_a_vencer' => 0
            ];
        }

        // Verificar se já temos este CPF neste PA
        if (!in_array($cpf_normalizado, $colaboradores_por_pa[$pa_key]['colaboradores_cpfs'])) {
            $colaboradores_por_pa[$pa_key]['colaboradores'][] = $colaborador;
            $colaboradores_por_pa[$pa_key]['colaboradores_cpfs'][] = $cpf_normalizado;
            $colaboradores_por_pa[$pa_key]['total_colaboradores']++;
            $colaboradores_por_pa[$pa_key]['total_cursos'] += $colaborador['total_cursos'];
            $colaboradores_por_pa[$pa_key]['total_concluidos'] += $colaborador['cursos_concluidos'];
            $colaboradores_por_pa[$pa_key]['total_vencidos'] += $colaborador['cursos_vencidos'];
            $colaboradores_por_pa[$pa_key]['total_a_vencer'] += $colaborador['cursos_a_vencer'];
        }
    }

    // Ordenar PAs por número
    uksort($colaboradores_por_pa, function($a, $b) {
        // Extrair número do PA para ordenação
        preg_match('/^(\d+|S\/PA)/', $a, $matches_a);
        preg_match('/^(\d+|S\/PA)/', $b, $matches_b);

        $num_a = $matches_a[1] === 'S/PA' ? 9999 : (int)$matches_a[1];
        $num_b = $matches_b[1] === 'S/PA' ? 9999 : (int)$matches_b[1];

        return $num_a <=> $num_b;
    });

    // Aplicar paginação por PA (não por colaboradores individuais)
    $total_pas = count($colaboradores_por_pa);
    $pas_per_page = 5; // Mostrar 5 PAs por página
    $total_pages = max(1, ceil($total_pas / $pas_per_page)); // Garantir pelo menos 1 página

    // Verificar se a página solicitada é válida
    if ($filtros['page'] > $total_pages) {
        $filtros['page'] = 1; // Voltar para primeira página se inválida
    }

    // Aplicar filtro por PA se especificado
    if (!empty($filtros['pa'])) {
        $colaboradores_por_pa_filtrados = [];
        foreach ($colaboradores_por_pa as $pa_key => $pa_data) {
            if ($pa_key === $filtros['pa']) {
                $colaboradores_por_pa_filtrados[$pa_key] = $pa_data;
                break;
            }
        }
        $colaboradores_por_pa = $colaboradores_por_pa_filtrados;
        $total_pas = count($colaboradores_por_pa);
        $total_pages = max(1, ceil($total_pas / $pas_per_page));
    }

    // Aplicar paginação aos PAs
    $offset_pa = ($filtros['page'] - 1) * $pas_per_page;
    $colaboradores_por_pa_paginados = array_slice($colaboradores_por_pa, $offset_pa, $pas_per_page, true);

    // Usar os PAs paginados para exibição
    $colaboradores_por_pa = $colaboradores_por_pa_paginados;

    // OTIMIZAÇÃO: Calcular métricas de forma mais eficiente
    $colaboradores_unicos = [];
    $total_cursos_colaboradores = 0;
    $total_vencidos_colaboradores = 0;
    $total_atribuicoes = 0;
    $total_em_andamento = 0;

    foreach ($todos_colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);

        // Só contar cada CPF uma vez
        if (!isset($colaboradores_unicos[$cpf_normalizado])) {
            $colaboradores_unicos[$cpf_normalizado] = $colaborador;
            $total_cursos_colaboradores += $colaborador['total_cursos'];
            $total_vencidos_colaboradores += $colaborador['cursos_vencidos'];
            $total_atribuicoes += $colaborador['total_cursos'];

            // OTIMIZAÇÃO: Calcular cursos em andamento usando dados já carregados
            $cpf = $colaborador['cpf'];
            $cursos_colaborador = $cursos_por_cpf[$cpf] ?? [];
            $em_andamento = 0;

            foreach ($cursos_colaborador as $curso) {
                // Curso em andamento: tem andamento_etapa mas não está aprovado
                if (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
                    $em_andamento++;
                }
            }

            $total_em_andamento += $em_andamento;
        }
    }

    // Contar total de colaboradores únicos (para estatísticas)
    $total_colaboradores = count($colaboradores_unicos);

    // Adicionar novas métricas às estatísticas
    $estatisticas['total_atribuicoes'] = $total_atribuicoes;
    $estatisticas['total_em_andamento'] = $total_em_andamento;
    $estatisticas['total_vencidos'] = $total_vencidos_colaboradores;

    // Atualizar estatísticas com o valor correto dos colaboradores únicos
    $estatisticas['total_colaboradores'] = $total_colaboradores;

    // Se não há PAs na página atual mas há colaboradores, mostrar todos na primeira página
    if (empty($colaboradores_por_pa) && !empty($todos_colaboradores) && $filtros['page'] == 1) {
        // Recriar agrupamento simples para primeira página
        $colaboradores_por_pa = [];
        $pa_key = "S/PA - Sem PA Definido";
        $colaboradores_por_pa[$pa_key] = [
            'info' => ['id' => 'sem_pa', 'numero' => 'S/PA', 'nome' => 'Sem PA Definido'],
            'colaboradores' => array_slice($todos_colaboradores, 0, $pas_per_page * 10), // Limitar para não sobrecarregar
            'total_colaboradores' => count($todos_colaboradores),
            'total_cursos' => array_sum(array_column($todos_colaboradores, 'total_cursos')),
            'total_concluidos' => array_sum(array_column($todos_colaboradores, 'cursos_concluidos')),
            'total_vencidos' => array_sum(array_column($todos_colaboradores, 'cursos_vencidos')),
            'total_a_vencer' => array_sum(array_column($todos_colaboradores, 'cursos_a_vencer'))
        ];
    }

    // Debug: verificar se há dados
    // echo "<!-- DEBUG: Total PAs: $total_pas, Página: {$filtros['page']}, PAs nesta página: " . count($colaboradores_por_pa) . " -->";
}



// OTIMIZAÇÃO: Cache para dados de filtros
$filtros_cache_key = 'filtros_dados';
$filtros_cache_file = EDU_API_CACHE_PATH . $filtros_cache_key . '.json';

$filtros_dados = null;
if (file_exists($filtros_cache_file) && (time() - filemtime($filtros_cache_file)) < 1800) { // Cache de 30 minutos
    $filtros_dados = json_decode(file_get_contents($filtros_cache_file), true);
}

if (!$filtros_dados) {
    // Buscar todos os dados de filtros em uma única consulta otimizada
    $filtros_query = "
        SELECT
            DISTINCT trilha,
            DISTINCT recurso,
            DISTINCT aprovacao
        FROM (
            SELECT DISTINCT trilha, NULL as recurso, NULL as aprovacao FROM edu_relatorio_educacao WHERE trilha IS NOT NULL AND trilha != ''
            UNION ALL
            SELECT NULL as trilha, DISTINCT recurso, NULL as aprovacao FROM edu_relatorio_educacao WHERE recurso IS NOT NULL AND recurso != ''
            UNION ALL
            SELECT NULL as trilha, NULL as recurso, DISTINCT aprovacao FROM edu_relatorio_educacao WHERE aprovacao IS NOT NULL AND aprovacao != ''
        ) as combined
        ORDER BY trilha, recurso, aprovacao
    ";

    // Consultas separadas mais eficientes
    $trilhas_query = "SELECT DISTINCT trilha FROM edu_relatorio_educacao WHERE trilha IS NOT NULL AND trilha != '' ORDER BY trilha";
    $cursos_query = "SELECT DISTINCT recurso FROM edu_relatorio_educacao WHERE recurso IS NOT NULL AND recurso != '' ORDER BY recurso";
    $aprovacoes_query = "SELECT DISTINCT aprovacao FROM edu_relatorio_educacao WHERE aprovacao IS NOT NULL AND aprovacao != '' ORDER BY aprovacao";

    $trilhas_disponiveis = $pdo_edu->query($trilhas_query)->fetchAll(PDO::FETCH_COLUMN);
    $cursos_disponiveis = $pdo_edu->query($cursos_query)->fetchAll(PDO::FETCH_COLUMN);
    $aprovacoes_disponiveis = $pdo_edu->query($aprovacoes_query)->fetchAll(PDO::FETCH_COLUMN);

    // Trilhas obrigatórias já foram carregadas anteriormente

    $filtros_dados = [
        'trilhas' => $trilhas_disponiveis,
        'cursos' => $cursos_disponiveis,
        'aprovacoes' => $aprovacoes_disponiveis
    ];

    // Salvar no cache
    file_put_contents($filtros_cache_file, json_encode($filtros_dados));
} else {
    $trilhas_disponiveis = $filtros_dados['trilhas'];
    $cursos_disponiveis = $filtros_dados['cursos'];
    $aprovacoes_disponiveis = $filtros_dados['aprovacoes'];
}

// OTIMIZAÇÃO: Função otimizada para calcular status de prazo
function calcularStatusPrazoOtimizado($curso, $prazos_config) {
    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

    // Se tem prazo personalizado e colaborador é elegível
    if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
        $config = $prazos_config[$key];
        $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
        $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

        if ($primeiro_prazo > 0) {
            // Lógica simplificada para prazos personalizados
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                if ($renovacao_prazo <= 0) {
                    return 'concluido_sem_renovacao'; // Sem renovação
                }
                // Calcular prazo de renovação
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $hoje = new DateTime();

                if ($prazo_renovacao < $hoje) {
                    return 'vencido';
                } elseif ($prazo_renovacao <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            } else {
                // Primeiro prazo
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $hoje = new DateTime();

                if ($primeiro_prazo_data < $hoje) {
                    return 'vencido';
                } elseif ($primeiro_prazo_data <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            }
        }
    }

    // Usar status básico já calculado na query
    return $curso['status_prazo_basico'] ?? 'em_dia';
}

// Função para formatar CPF
function formatarCpf($cpf) {
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

// Consultas específicas para aba de cursos
$cursos_data = [];
$total_cursos_count = 0;
$total_pages_cursos = 0;

if ($aba_ativa === 'cursos') {
    // Definir offset para paginação de cursos
    $offset_cursos = ($filtros['page'] - 1) * EDU_RECORDS_PER_PAGE;

    // Criar condições específicas para cursos (separadas das de colaboradores)
    $where_conditions_cursos = [];
    $params_cursos = [];

    // Filtros comuns
    if (!empty($filtros['trilha'])) {
        $where_conditions_cursos[] = "trilha LIKE ?";
        $params_cursos[] = '%' . $filtros['trilha'] . '%';
    }

    if (!empty($filtros['data_inicio'])) {
        $where_conditions_cursos[] = "data_importacao >= ?";
        $params_cursos[] = $filtros['data_inicio'] . ' 00:00:00';
    }

    if (!empty($filtros['data_fim'])) {
        $where_conditions_cursos[] = "data_importacao <= ?";
        $params_cursos[] = $filtros['data_fim'] . ' 23:59:59';
    }

    // Filtros específicos para cursos
    if (!empty($filtros['curso'])) {
        $where_conditions_cursos[] = "recurso LIKE ?";
        $params_cursos[] = '%' . $filtros['curso'] . '%';
    }

    if (!empty($filtros['aprovacao'])) {
        $where_conditions_cursos[] = "aprovacao = ?";
        $params_cursos[] = $filtros['aprovacao'];
    }

    // Query para cursos com métricas (LIMIT e OFFSET diretos na query)
    $cursos_query = "
        SELECT
            codigo_recurso,
            recurso,
            trilha,
            codigo_trilha,
            carga_horaria_recurso,
            COUNT(DISTINCT cpf) as total_colaboradores,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
            COUNT(*) as total_registros,
            AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as vencidos,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions_cursos) ? "WHERE " . implode(" AND ", $where_conditions_cursos) : "") . "
        GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
        ORDER BY recurso
        LIMIT " . (int)EDU_RECORDS_PER_PAGE . " OFFSET " . (int)$offset_cursos;

    $stmt_cursos = $pdo_edu->prepare($cursos_query);
    $stmt_cursos->execute($params_cursos);
    $cursos_data = $stmt_cursos->fetchAll();

    // Contar total de cursos para paginação
    $count_cursos_query = "
        SELECT COUNT(DISTINCT CONCAT(codigo_recurso, '-', recurso)) as total
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions_cursos) ? "WHERE " . implode(" AND ", $where_conditions_cursos) : "");

    $stmt_count_cursos = $pdo_edu->prepare($count_cursos_query);
    $stmt_count_cursos->execute($params_cursos);
    $total_cursos_count = $stmt_count_cursos->fetch()['total'];
    $total_pages_cursos = ceil($total_cursos_count / EDU_RECORDS_PER_PAGE);
}

// Função para verificar se um curso está no período especificado
function verificarCursoNoPeriodo($prazo_curso, $periodo) {
    $hoje = new DateTime();

    switch ($periodo) {
        case 'mes_atual':
            // Cursos que vencem no mês atual
            return $prazo_curso->format('Y-m') === $hoje->format('Y-m');

        case 'proximo_mes':
            // Cursos que vencem no próximo mês
            $proximo_mes = (clone $hoje)->modify('+1 month');
            return $prazo_curso->format('Y-m') === $proximo_mes->format('Y-m');

        case 'proximos_30_dias':
            // Cursos que vencem nos próximos 30 dias
            $limite = (clone $hoje)->modify('+30 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;

        case 'proximos_60_dias':
            // Cursos que vencem nos próximos 60 dias
            $limite = (clone $hoje)->modify('+60 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;

        case 'proximos_90_dias':
            // Cursos que vencem nos próximos 90 dias
            $limite = (clone $hoje)->modify('+90 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;

        case 'vencidos_30_dias':
            // Cursos vencidos nos últimos 30 dias
            $limite = (clone $hoje)->modify('-30 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;

        case 'vencidos_60_dias':
            // Cursos vencidos nos últimos 60 dias
            $limite = (clone $hoje)->modify('-60 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;

        case 'vencidos_90_dias':
            // Cursos vencidos nos últimos 90 dias
            $limite = (clone $hoje)->modify('-90 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;

        case 'trimestre_atual':
            // Cursos que vencem no trimestre atual
            $mes_atual = (int)$hoje->format('n');
            $trimestre_inicio = (floor(($mes_atual - 1) / 3) * 3) + 1;
            $trimestre_fim = $trimestre_inicio + 2;

            $inicio_trimestre = new DateTime($hoje->format('Y') . '-' . str_pad($trimestre_inicio, 2, '0', STR_PAD_LEFT) . '-01');
            $fim_trimestre = (clone $inicio_trimestre)->modify('+3 months -1 day');

            return $prazo_curso >= $inicio_trimestre && $prazo_curso <= $fim_trimestre;

        case 'semestre_atual':
            // Cursos que vencem no semestre atual
            $mes_atual = (int)$hoje->format('n');
            $semestre_inicio = $mes_atual <= 6 ? 1 : 7;

            $inicio_semestre = new DateTime($hoje->format('Y') . '-' . str_pad($semestre_inicio, 2, '0', STR_PAD_LEFT) . '-01');
            $fim_semestre = (clone $inicio_semestre)->modify('+6 months -1 day');

            return $prazo_curso >= $inicio_semestre && $prazo_curso <= $fim_semestre;

        case 'ano_atual':
            // Cursos que vencem no ano atual
            return $prazo_curso->format('Y') === $hoje->format('Y');

        default:
            return true;
    }
}

// Função para calcular status do colaborador
function calcularStatusColaborador($colaborador) {
    global $pdo_edu, $prazos_config;

    $total_cursos = $colaborador['total_cursos'];
    $cursos_concluidos = $colaborador['cursos_concluidos'];
    $cursos_vencidos = $colaborador['cursos_vencidos'];
    $cursos_a_vencer = $colaborador['cursos_a_vencer'];

    // Verificar se há cursos em andamento
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $tem_em_andamento = false;

    foreach ($cursos_colaborador as $curso) {
        if (!empty($curso['andamento_etapa']) &&
            $curso['aprovacao'] !== 'Sim' &&
            $curso['status_prazo'] !== 'vencido' &&
            $curso['status_prazo'] !== 'a_vencer') {
            $tem_em_andamento = true;
            break;
        }
    }

    if ($cursos_vencidos > 0) {
        return ['status' => 'vencido', 'texto' => 'Cursos Vencidos', 'classe' => 'danger'];
    } elseif ($cursos_a_vencer > 0) {
        return ['status' => 'a_vencer', 'texto' => 'A Vencer', 'classe' => 'warning'];
    } elseif ($tem_em_andamento) {
        return ['status' => 'em_andamento', 'texto' => 'Em Andamento', 'classe' => 'sicoob-turquesa'];
    } elseif ($cursos_concluidos > 0) {
        return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'sicoob-verde-claro'];
    } else {
        return ['status' => 'pendente', 'texto' => 'Pendente', 'classe' => 'secondary'];
    }
}

// Função para calcular status do curso
function calcularStatusCurso($curso) {
    $total_colaboradores = $curso['total_colaboradores'];
    $total_aprovados = $curso['total_aprovados'];
    $total_concluidos = $curso['total_concluidos'];

    if ($total_colaboradores == 0) {
        return ['status' => 'sem_dados', 'texto' => 'Sem Dados', 'classe' => 'secondary'];
    }

    $percentual_aprovacao = ($total_aprovados / $total_colaboradores) * 100;

    if ($percentual_aprovacao >= 80) {
        return ['status' => 'excelente', 'texto' => 'Excelente', 'classe' => 'success'];
    } elseif ($percentual_aprovacao >= 60) {
        return ['status' => 'bom', 'texto' => 'Bom', 'classe' => 'primary'];
    } elseif ($percentual_aprovacao >= 40) {
        return ['status' => 'regular', 'texto' => 'Regular', 'classe' => 'warning'];
    } else {
        return ['status' => 'baixo', 'texto' => 'Baixo', 'classe' => 'danger'];
    }
}

// OTIMIZAÇÃO: Função otimizada para buscar cursos de um colaborador
function buscarCursosColaborador($cpf, $pdo, $prazos_config) {
    // Usar cache global se disponível
    global $cursos_por_cpf;

    if (isset($cursos_por_cpf[$cpf])) {
        $cursos = $cursos_por_cpf[$cpf];
    } else {
        // Fallback para consulta individual (compatibilidade)
        $query = "
            SELECT
                codigo_trilha,
                trilha,
                codigo_recurso,
                recurso,
                aprovacao,
                data_conclusao,
                concluir_trilha_ate,
                data_admissao,
                nota_recurso,
                aproveitamento,
                validade_recurso,
                andamento_etapa,
                CASE
                    WHEN concluir_trilha_ate < CURDATE() AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'vencido'
                    WHEN concluir_trilha_ate >= CURDATE() AND concluir_trilha_ate <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'a_vencer'
                    ELSE 'em_dia'
                END as status_prazo_basico
            FROM edu_relatorio_educacao
            WHERE cpf = ?
            ORDER BY trilha, recurso
        ";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$cpf]);
        $cursos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // OTIMIZAÇÃO: Calcular prazos de forma mais eficiente
    foreach ($cursos as &$curso) {
        $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);
        $curso['status_prazo'] = $status_final;

        // Calcular prazo calculado se necessário para compatibilidade
        $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
            $config = $prazos_config[$key];
            $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
            $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00' && $renovacao_prazo > 0) {
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $curso['prazo_calculado'] = $prazo_renovacao->format('Y-m-d');
            } elseif (empty($curso['data_conclusao']) || $curso['data_conclusao'] === '0000-00-00') {
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $curso['prazo_calculado'] = $primeiro_prazo_data->format('Y-m-d');
            } else {
                $curso['prazo_calculado'] = null;
            }
            $curso['prazo_personalizado'] = true;
        } else {
            $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
            $curso['prazo_personalizado'] = false;
        }

        // Calcular dias para o prazo
        if (!empty($curso['prazo_calculado'])) {
            $hoje = new DateTime();
            $prazo = new DateTime($curso['prazo_calculado']);
            $diff = $hoje->diff($prazo);
            $curso['dias_prazo'] = $prazo < $hoje ? -$diff->days : $diff->days;
        } else {
            $curso['dias_prazo'] = null;
        }
    }

    return $cursos;
}

// Função para calcular prazo personalizado
function calcularPrazoPersonalizado($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    // Validar parâmetros de entrada
    if (empty($cpf) || empty($codigo_trilha) || empty($codigo_recurso) || empty($data_admissao)) {
        return $prazo_padrao;
    }

    // Validar data de admissão
    if ($data_admissao === '0000-00-00' || !strtotime($data_admissao)) {
        return $prazo_padrao;
    }

    // REGRA: Prazos personalizados só valem para colaboradores admitidos após 01/01/2023
    $data_corte = '2023-01-01';
    if ($data_admissao <= $data_corte) {
        return $prazo_padrao; // Usar prazo padrão para colaboradores antigos
    }

    // Buscar configuração do prazo personalizado
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";

    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);

    if (!$config) {
        return $prazo_padrao; // Fallback para prazo padrão
    }

    // Validar se os valores de prazo são válidos
    $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
    $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

    if ($primeiro_prazo <= 0) {
        return $prazo_padrao; // Fallback se primeiro prazo inválido
    }

    // Verificar se já houve conclusões anteriores
    $query_conclusoes = "
        SELECT data_conclusao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
        AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
        ORDER BY data_conclusao DESC
        LIMIT 1
    ";

    $stmt_conclusoes = $pdo->prepare($query_conclusoes);
    $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
    $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);

    try {
        if ($ultima_conclusao) {
            // NOVA REGRA: Se renovação está vazia (0), curso não tem renovação
            if ($renovacao_prazo <= 0) {
                return null; // Curso concluído e sem renovação - sem prazo
            }

            // Renovação: usar data da última conclusão + prazo de renovação
            // Validar data de conclusão
            if ($ultima_conclusao['data_conclusao'] === '0000-00-00' || !strtotime($ultima_conclusao['data_conclusao'])) {
                return $prazo_padrao;
            }

            $data_base = new DateTime($ultima_conclusao['data_conclusao']);
            $data_base->add(new DateInterval('P' . $renovacao_prazo . 'D'));
        } else {
            // Primeira vez: usar data de admissão + primeiro prazo
            $data_base = new DateTime($data_admissao);
            $data_base->add(new DateInterval('P' . $primeiro_prazo . 'D'));
        }

        return $data_base->format('Y-m-d');

    } catch (Exception $e) {
        // Em caso de erro, retornar prazo padrão
        return $prazo_padrao;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análise - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }



        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-top: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(0, 174, 157, 0.1), rgba(201, 210, 0, 0.1));
            color: var(--sicoob-verde-escuro);
        }

        /* Estilo do cabeçalho dos filtros */
        .filter-section .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border-bottom: none;
        }

        .filter-section .card-header .btn-link {
            color: var(--sicoob-branco) !important;
            text-decoration: none !important;
        }

        .filter-section .card-header .btn-link:hover {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .filter-section .card-header h5 {
            color: var(--sicoob-branco) !important;
            font-weight: 600;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
            font-weight: 600;
        }

        .stats-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }

        .stats-label {
            color: var(--sicoob-cinza);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
        }

        .btn-outline-primary {
            border-color: var(--sicoob-turquesa);
            color: var(--sicoob-turquesa);
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0.6rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .colaborador-card {
            border-left: 4px solid var(--sicoob-verde-claro);
            transition: all 0.3s ease;
        }

        .colaborador-card:hover {
            border-left-color: var(--sicoob-turquesa);
        }

        .badge-custom {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }

        /* Badges personalizados com cores da identidade visual Sicoob */
        .bg-sicoob-turquesa {
            background-color: var(--sicoob-turquesa) !important;
            color: var(--sicoob-branco) !important;
        }

        .bg-sicoob-verde-claro {
            background-color: var(--sicoob-verde-claro) !important;
            color: var(--sicoob-verde-escuro) !important;
            font-weight: 600;
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }

        .progress-bar-custom {
            background: linear-gradient(90deg, var(--sicoob-verde-claro), var(--sicoob-turquesa));
            border-radius: 10px;
        }

        .filter-section {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Melhorias na seção de filtros */
        .filter-section .form-label {
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .filter-section .form-label i {
            color: var(--sicoob-turquesa);
            width: 16px;
            text-align: center;
        }

        .filter-section .form-control,
        .filter-section .form-select {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0.6rem 0.75rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .filter-section .form-control:focus,
        .filter-section .form-select:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.15);
            transform: translateY(-1px);
        }

        .filter-section .form-text {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .filter-section .alert-info {
            background: linear-gradient(135deg, rgba(0, 174, 157, 0.1), rgba(201, 210, 0, 0.1));
            border: 1px solid rgba(0, 174, 157, 0.2);
            color: var(--sicoob-verde-escuro);
        }

        .filter-section .btn {
            border-radius: 8px;
            font-weight: 600;
            padding: 0.6rem 1.5rem;
            transition: all 0.3s ease;
        }

        .filter-section .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Espaçamento harmonioso entre linhas de filtros */
        .filter-section .row + .row {
            margin-top: 1rem;
        }

        /* Alinhamento dos campos de múltipla seleção */
        .filter-section select[multiple] {
            resize: vertical;
            min-height: 120px;
        }

        .filter-section select[multiple] option {
            padding: 0.4rem 0.6rem;
            margin: 1px 0;
            border-radius: 4px;
        }

        .filter-section select[multiple] option:checked {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            color: white;
        }

        /* Cabeçalho da seção principal de Colaboradores - gradiente invertido dos PAs */
        .colaboradores-header {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro)) !important;
            color: var(--sicoob-branco) !important;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
        }

        .colaboradores-header:hover {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)) !important;
            transform: translateY(-1px);
        }

        .colaboradores-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--sicoob-branco) !important;
        }

        .colaboradores-stats {
            color: var(--sicoob-branco);
            font-weight: 500;
        }

        .colaboradores-stats span {
            margin: 0 0.75rem;
            font-size: 0.9rem;
        }

        /* Manter a cor vermelha para "vencidos" no cabeçalho dos colaboradores */
        .colaboradores-stats .text-danger {
            color: #dc3545 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
            font-weight: 600;
        }

        .colaboradores-header .btn-outline-light {
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .colaboradores-header .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: white;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .pagination .page-link {
            color: var(--sicoob-turquesa);
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white !important;
        }

        .intranet-info {
            background: linear-gradient(135deg, #e8f5f3, #f0f9f8);
            border-radius: 8px;
            padding: 0.8rem;
            margin-top: 0.5rem;
            border-left: 3px solid var(--sicoob-turquesa);
        }

        .no-intranet {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left-color: #ffc107;
        }

        /* Estilos das Abas */
        .nav-tabs {
            border-bottom: 2px solid var(--sicoob-turquesa);
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 12px 12px 0 0;
            padding: 0.5rem 1rem 0 1rem;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            color: var(--sicoob-cinza);
            font-weight: 600;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
            transition: all 0.3s ease;
            background: transparent;
        }

        .nav-tabs .nav-link:hover {
            border: none;
            background: linear-gradient(135deg, rgba(0, 174, 157, 0.1), rgba(201, 210, 0, 0.1));
            color: var(--sicoob-verde-escuro);
            transform: translateY(-2px);
        }

        .nav-tabs .nav-link.active {
            border: none;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            box-shadow: 0 4px 12px rgba(0, 54, 65, 0.3);
        }

        .nav-tabs .nav-link.active:hover {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            transform: translateY(-2px);
        }

        .tab-content {
            background: transparent;
            border: none;
            padding: 0;
        }

        .tabs-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Estilos das Seções de PA */
        .pa-section {
            margin-bottom: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .pa-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: left;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pa-header:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
            transform: translateY(-1px);
        }

        .pa-header.collapsed {
            border-radius: 12px;
        }

        .pa-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .pa-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .pa-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .pa-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .pa-content {
            background: var(--sicoob-branco);
            padding: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pa-content.d-none {
            display: none !important;
        }

        .pa-content.show {
            display: block !important;
        }

        .pa-content {
            transition: all 0.3s ease;
        }

        .pa-summary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .pa-summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            text-align: center;
        }

        .pa-summary-stat {
            background: var(--sicoob-branco);
            padding: 0.8rem;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .pa-summary-stat .number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }

        .pa-summary-stat .label {
            font-size: 0.8rem;
            color: var(--sicoob-cinza);
            margin-top: 0.2rem;
        }

        /* Animações */
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 1000px;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 1;
                max-height: 1000px;
            }
            to {
                opacity: 0;
                max-height: 0;
            }
        }

        .pa-content.show {
            animation: slideDown 0.3s ease-out;
        }

        @media (max-width: 768px) {
            .stats-number {
                font-size: 1.5rem;
            }
            
            .filter-section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-chart-line me-2"></i>Análise de Dados</h2>
                <p class="text-muted">Análise detalhada dos colaboradores referente às atribuições de trilhas/cursos</p>
            </div>
        </div>



        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_colaboradores']); ?></div>
                        <div class="stats-label">Colaboradores</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_trilhas']); ?></div>
                        <div class="stats-label">Trilhas Disponíveis</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_cursos']); ?></div>
                        <div class="stats-label">Cursos Cadastrados</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_atribuicoes']); ?></div>
                        <div class="stats-label">Cursos Atribuídos</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['cursos_aprovados']); ?></div>
                        <div class="stats-label">Cursos Concluidos</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_em_andamento']); ?></div>
                        <div class="stats-label">Cursos em Andamento</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_vencidos']); ?></div>
                        <div class="stats-label">Cursos Vencidos</div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['media_aproveitamento'] ?? 0, 1); ?>%</div>
                        <div class="stats-label">Média de Aproveitamento</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seção de Filtros -->
        <div class="filter-section">
            <div class="card">
                <div class="card-header">
                    <button class="btn btn-link text-decoration-none p-0 w-100 text-start" type="button"
                            data-bs-toggle="collapse" data-bs-target="#filtrosCollapse"
                            aria-expanded="true" aria-controls="filtrosCollapse">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filtros de Pesquisa
                            <i class="fas fa-chevron-down float-end mt-1"></i>
                        </h5>
                    </button>
                </div>
                <div class="collapse show" id="filtrosCollapse">
                    <div class="card-body">
                        <form method="GET" action="" id="filtroForm">
                            <input type="hidden" name="aba" value="<?php echo $aba_ativa; ?>">

                            <!-- Primeira linha de filtros - Filtros básicos -->
                            <div class="row align-items-end">
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <label for="cpf" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>CPF
                                    </label>
                                    <input type="text" class="form-control" id="cpf" name="cpf"
                                           value="<?php echo htmlspecialchars($filtros['cpf']); ?>"
                                           placeholder="000.000.000-00" maxlength="14">
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                    <label for="nome" class="form-label">
                                        <i class="fas fa-user me-1"></i>Nome do Colaborador
                                    </label>
                                    <input type="text" class="form-control" id="nome" name="nome"
                                           value="<?php echo htmlspecialchars($filtros['nome']); ?>"
                                           placeholder="Digite o nome">
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <label for="pa" class="form-label">
                                        <i class="fas fa-building me-1"></i>PA
                                    </label>
                                    <select class="form-select" id="pa" name="pa">
                                        <option value="">Todos os PAs</option>
                                        <?php
                                        // Buscar PAs disponíveis baseado na API da intranet
                                        $pas_disponiveis = [];
                                        foreach ($mapa_agencias as $agencia_id => $agencia_data) {
                                            $pa_key = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                                            $pas_disponiveis[$pa_key] = $pa_key;
                                        }
                                        // Adicionar "Sem PA" se houver colaboradores sem PA
                                        $pas_disponiveis['S/PA - Sem PA Definido'] = 'S/PA - Sem PA Definido';
                                        ksort($pas_disponiveis);

                                        foreach ($pas_disponiveis as $pa): ?>
                                        <option value="<?php echo htmlspecialchars($pa); ?>"
                                                <?php echo $filtros['pa'] === $pa ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pa); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-lg-2 col-md-6 col-sm-6 mb-3">
                                    <label for="trilha" class="form-label">
                                        <i class="fas fa-route me-1"></i>Trilha
                                    </label>
                                    <select class="form-select" id="trilha" name="trilha">
                                        <option value="">Todas as trilhas</option>
                                        <?php foreach ($trilhas_disponiveis as $trilha): ?>
                                        <option value="<?php echo htmlspecialchars($trilha); ?>"
                                                <?php echo $filtros['trilha'] === $trilha ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($trilha); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-lg-2 col-md-6 col-sm-6 mb-3">
                                    <label for="trilha_obrigatoria" class="form-label">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Trilhas Obrigatórias
                                    </label>
                                    <select class="form-select" id="trilha_obrigatoria" name="trilha_obrigatoria">
                                        <option value="">Todas</option>
                                        <option value="sim" <?php echo $filtros['trilha_obrigatoria'] === 'sim' ? 'selected' : ''; ?>>Apenas Obrigatórias</option>
                                        <option value="nao" <?php echo $filtros['trilha_obrigatoria'] === 'nao' ? 'selected' : ''; ?>>Apenas Não Obrigatórias</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Segunda linha de filtros - Filtros avançados -->
                            <div class="row align-items-start">
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <label for="status_curso" class="form-label">
                                        <i class="fas fa-tasks me-1"></i>Status do Colaborador
                                        <small class="text-muted">(múltipla seleção)</small>
                                    </label>
                                    <select class="form-select" id="status_curso" name="status_curso[]" multiple size="4"
                                            style="min-height: 120px;">
                                        <option value="aprovado" <?php echo in_array('aprovado', $filtros['status_curso']) ? 'selected' : ''; ?>>
                                            ✅ Aprovado
                                        </option>
                                        <option value="em_andamento" <?php echo in_array('em_andamento', $filtros['status_curso']) ? 'selected' : ''; ?>>
                                            🔄 Em Andamento
                                        </option>
                                        <option value="a_vencer" <?php echo in_array('a_vencer', $filtros['status_curso']) ? 'selected' : ''; ?>>
                                            ⏰ A Vencer
                                        </option>
                                        <option value="vencido" <?php echo in_array('vencido', $filtros['status_curso']) ? 'selected' : ''; ?>>
                                            🔴 Cursos Vencidos
                                        </option>
                                    </select>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Ctrl+clique para múltiplas opções. Colaborador deve ter cursos com TODOS os status selecionados.
                                    </small>
                                </div>

                                <div class="col-lg-4 col-md-6 mb-3">
                                    <label for="periodo_prazo" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>Período dos Prazos
                                    </label>
                                    <select class="form-select" id="periodo_prazo" name="periodo_prazo">
                                        <option value="">Todos os períodos</option>
                                        <optgroup label="📅 Períodos Futuros">
                                            <option value="mes_atual" <?php echo $filtros['periodo_prazo'] === 'mes_atual' ? 'selected' : ''; ?>>📅 Mês Atual</option>
                                            <option value="proximo_mes" <?php echo $filtros['periodo_prazo'] === 'proximo_mes' ? 'selected' : ''; ?>>📅 Próximo Mês</option>
                                            <option value="proximos_30_dias" <?php echo $filtros['periodo_prazo'] === 'proximos_30_dias' ? 'selected' : ''; ?>>⏰ Próximos 30 dias</option>
                                            <option value="proximos_60_dias" <?php echo $filtros['periodo_prazo'] === 'proximos_60_dias' ? 'selected' : ''; ?>>⏰ Próximos 60 dias</option>
                                            <option value="proximos_90_dias" <?php echo $filtros['periodo_prazo'] === 'proximos_90_dias' ? 'selected' : ''; ?>>⏰ Próximos 90 dias</option>
                                            <option value="trimestre_atual" <?php echo $filtros['periodo_prazo'] === 'trimestre_atual' ? 'selected' : ''; ?>>📊 Trimestre Atual</option>
                                            <option value="semestre_atual" <?php echo $filtros['periodo_prazo'] === 'semestre_atual' ? 'selected' : ''; ?>>📊 Semestre Atual</option>
                                            <option value="ano_atual" <?php echo $filtros['periodo_prazo'] === 'ano_atual' ? 'selected' : ''; ?>>📊 Ano Atual</option>
                                        </optgroup>
                                        <optgroup label="🔴 Períodos Vencidos">
                                            <option value="vencidos_30_dias" <?php echo $filtros['periodo_prazo'] === 'vencidos_30_dias' ? 'selected' : ''; ?>>🔴 Vencidos nos últimos 30 dias</option>
                                            <option value="vencidos_60_dias" <?php echo $filtros['periodo_prazo'] === 'vencidos_60_dias' ? 'selected' : ''; ?>>🔴 Vencidos nos últimos 60 dias</option>
                                            <option value="vencidos_90_dias" <?php echo $filtros['periodo_prazo'] === 'vencidos_90_dias' ? 'selected' : ''; ?>>🔴 Vencidos nos últimos 90 dias</option>
                                        </optgroup>
                                    </select>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Filtra colaboradores que possuem cursos com prazos no período selecionado.
                                    </small>
                                </div>
                                <div class="col-lg-4 col-md-12 mb-3">
                                    <div class="alert alert-info mb-0 w-100" style="padding: 0.75rem; border-radius: 8px;">
                                        <div class="d-flex align-items-start">
                                            <div>
                                                <strong class="d-block mb-1">💡 Dica de Uso:</strong>
                                                <small class="text-muted">
                                                    Combine filtros para buscas específicas:<br>
                                                    • Status "A Vencer" + Período "Próximo Mês"<br>
                                                    • Status "Vencidos" + Período "Últimos 30 dias"
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Linha de botões -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-center gap-3 pt-3 border-top">
                                        <button type="submit" class="btn btn-primary px-4">
                                            <i class="fas fa-search me-2"></i>Aplicar Filtros
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary px-4" onclick="limparFiltrosComConfirmacao()">
                                            <i class="fas fa-eraser me-2"></i>Limpar Filtros
                                        </button>
                                        <button type="button" class="btn btn-outline-primary px-4" onclick="toggleFiltrosAvancados()">
                                            <i class="fas fa-cog me-2"></i>Filtros Avançados
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>



        <!-- Análise de Colaboradores -->
                <div class="card">
                    <div class="card-header colaboradores-header">
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <div>
                                <h5 class="colaboradores-title mb-1">
                                    <i class="fas fa-users me-2"></i>Colaboradores
                                </h5>
                            </div>
                            <div class="colaboradores-stats text-center flex-grow-1 mx-4">
                                <span><i class="fas fa-users me-1"></i><?php echo number_format($total_colaboradores); ?> encontrados</span>
                                <span><i class="fas fa-graduation-cap me-1"></i><?php echo number_format($total_cursos_colaboradores); ?> cursos</span>
                                <?php if ($total_vencidos_colaboradores > 0): ?>
                                    <span class="text-danger" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.7); font-weight: 600;"><i class="fas fa-exclamation-triangle me-1"></i><?php echo number_format($total_vencidos_colaboradores); ?> vencidos</span>
                                <?php endif; ?>
                            </div>
                            <div class="d-flex gap-2">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-light btn-sm" onclick="toggleAllPASections(true)" title="Expandir Todas as Seções">
                                        <i class="fas fa-expand-alt me-1"></i> Expandir
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="toggleAllPASections(false)" title="Retrair Todas as Seções">
                                        <i class="fas fa-compress-alt me-1"></i> Retrair
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
            <div class="card-body">
                <?php if (empty($colaboradores_por_pa)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum colaborador encontrado</h5>
                    <p class="text-muted">Tente ajustar os filtros de pesquisa</p>
                </div>
                <?php else: ?>
                <!-- Seções dos Colaboradores por PA -->
                <?php foreach ($colaboradores_por_pa as $pa_nome => $pa_data): ?>
                    <div class="pa-section">
                        <!-- Cabeçalho da Seção PA -->
                        <button class="pa-header" type="button"
                                data-bs-target="#pa-<?php echo md5($pa_nome); ?>"
                                aria-expanded="true"
                                aria-controls="pa-<?php echo md5($pa_nome); ?>">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div>
                                    <h5 class="pa-title">
                                        <i class="fas fa-building me-2"></i>
                                        <?php echo htmlspecialchars($pa_nome); ?>
                                    </h5>
                                </div>
                                <div class="pa-stats text-center flex-grow-1 mx-4">
                                    <span><i class="fas fa-users me-1"></i><?php echo $pa_data['total_colaboradores']; ?> colaboradores</span>
                                    <span><i class="fas fa-graduation-cap me-1"></i><?php echo $pa_data['total_cursos']; ?> cursos</span>
                                    <?php if ($pa_data['total_vencidos'] > 0): ?>
                                        <span class="text-danger" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.7); font-weight: 600;"><i class="fas fa-exclamation-triangle me-1"></i><?php echo $pa_data['total_vencidos']; ?> vencidos</span>
                                    <?php endif; ?>
                                </div>
                                <div style="width: 24px;"></div> <!-- Espaço para balancear o ícone de toggle -->
                            </div>
                            <i class="fas fa-chevron-down pa-toggle"></i>
                        </button>

                        <!-- Conteúdo da Seção PA -->
                        <div class="pa-content show" id="pa-<?php echo md5($pa_nome); ?>" style="display: block;">
                                <!-- Resumo do PA -->
                                <div class="pa-summary">
                                    <div class="pa-summary-stats">
                                        <div class="pa-summary-stat">
                                            <div class="number"><?php echo $pa_data['total_colaboradores']; ?></div>
                                            <div class="label">Colaboradores</div>
                                        </div>
                                        <div class="pa-summary-stat">
                                            <div class="number"><?php echo $pa_data['total_cursos']; ?></div>
                                            <div class="label">Cursos Atribuídos</div>
                                        </div>
                                        <div class="pa-summary-stat">
                                            <div class="number text-success"><?php echo $pa_data['total_concluidos']; ?></div>
                                            <div class="label">Concluídos</div>
                                        </div>
                                        <div class="pa-summary-stat">
                                            <div class="number text-warning"><?php echo $pa_data['total_a_vencer']; ?></div>
                                            <div class="label">A Vencer</div>
                                        </div>
                                        <div class="pa-summary-stat">
                                            <div class="number text-danger"><?php echo $pa_data['total_vencidos']; ?></div>
                                            <div class="label">Vencidos</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cards dos Colaboradores do PA -->
                                <div class="row">
                                <?php foreach ($pa_data['colaboradores'] as $colaborador): ?>
                                    <?php
                                        $status = calcularStatusColaborador($colaborador);
                                        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
                                        $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
                                        $percentual_conclusao = $colaborador['total_cursos'] > 0 ?
                                            ($colaborador['cursos_concluidos'] / $colaborador['total_cursos']) * 100 : 0;
                                    ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card colaborador-card h-100">
                            <div class="card-body">
                                <!-- Cabeçalho do Colaborador -->
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="d-flex align-items-center flex-grow-1">
                                        <!-- Foto do Colaborador -->
                                        <div class="me-3">
                                            <?php if ($usuario_intranet && !empty($usuario_intranet['foto_url'])): ?>
                                                <img src="<?php echo htmlspecialchars($usuario_intranet['foto_url']); ?>"
                                                     alt="Foto do colaborador"
                                                     class="rounded-circle"
                                                     style="width: 50px; height: 50px; object-fit: cover; border: 2px solid #e9ecef;"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div class="rounded-circle bg-light d-none align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; border: 2px solid #e9ecef;">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; border: 2px solid #e9ecef;">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1 fw-bold">
                                                <?php
                                                // Priorizar nome da Intranet, senão usar do sistema
                                                $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
                                                echo htmlspecialchars($nome_exibir);
                                                ?>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-id-card me-1"></i>
                                                <?php echo formatarCpf($colaborador['cpf']); ?>
                                            </small>
                                        </div>
                                    </div>
                                    <span class="badge badge-custom bg-<?php echo $status['classe']; ?>">
                                        <?php echo $status['texto']; ?>
                                    </span>
                                </div>

                                <!-- Informações Unificadas do Colaborador -->
                                <div class="colaborador-info mb-3">
                                    <?php
                                    // Unificar informações priorizando dados da Intranet
                                    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
                                    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
                                    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';

                                    // Buscar informações da agência
                                    $agencia_info = 'N/A';
                                    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                                        $agencia_id = $usuario_intranet['agencia'];
                                        if (isset($mapa_agencias[$agencia_id])) {
                                            $agencia_data = $mapa_agencias[$agencia_id];
                                            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                                        } else {
                                            $agencia_info = $agencia_id; // Fallback para ID se não encontrar
                                        }
                                    }
                                    ?>

                                    <div class="row g-1">
                                        <div class="col-12">
                                            <small><strong>E-mail:</strong> <?php echo htmlspecialchars($email_exibir); ?></small>
                                        </div>
                                        <div class="col-12">
                                            <small><strong>Agência:</strong> <?php echo htmlspecialchars($agencia_info); ?></small>
                                        </div>
                                        <div class="col-12">
                                            <small><strong>Setor:</strong> <?php echo htmlspecialchars($setor_exibir); ?></small>
                                        </div>
                                        <div class="col-12">
                                            <small><strong>Função:</strong> <?php echo htmlspecialchars($funcao_exibir); ?></small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Métricas do Colaborador -->
                                <div class="row g-1 mt-3">
                                    <div class="col-3">
                                        <div class="text-center">
                                            <div class="fw-bold text-primary"><?php echo $colaborador['total_cursos']; ?></div>
                                            <small class="text-muted">Atribuídos</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-center">
                                            <div class="fw-bold text-success"><?php echo $colaborador['cursos_concluidos']; ?></div>
                                            <small class="text-muted">Concluídos</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-center">
                                            <div class="fw-bold text-warning"><?php echo $colaborador['cursos_a_vencer']; ?></div>
                                            <small class="text-muted">A Vencer</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-center">
                                            <div class="fw-bold text-danger"><?php echo $colaborador['cursos_vencidos']; ?></div>
                                            <small class="text-muted">Vencidos</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progresso -->
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="text-muted">Progresso de Conclusão</small>
                                        <small class="fw-bold"><?php echo number_format($percentual_conclusao, 1); ?>%</small>
                                    </div>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar progress-bar-custom"
                                             style="width: <?php echo $percentual_conclusao; ?>%"></div>
                                    </div>
                                </div>

                                <!-- Informações Adicionais -->
                                <div class="row g-2 mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Última atualização: <?php echo date('d/m/Y', strtotime($colaborador['ultima_atualizacao'])); ?>
                                        </small>
                                    </div>
                                    <?php if ($colaborador['media_aproveitamento']): ?>
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="fas fa-chart-line me-1"></i>
                                            Média de aproveitamento: <?php echo number_format($colaborador['media_aproveitamento'], 1); ?>%
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Botão de Detalhes -->
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary btn-sm w-100"
                                            onclick="verDetalhes('<?php echo $colaborador['cpf']; ?>')">
                                        <i class="fas fa-eye me-1"></i> Ver Detalhes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                                </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                <?php endif; ?>

                <!-- Paginação para Colaboradores -->
                <?php if ($aba_ativa === 'colaboradores' && $total_pages > 1): ?>
                <nav aria-label="Navegação de páginas" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <!-- Primeira página -->
                        <?php if ($filtros['page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['page' => 1])); ?>">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['page' => $filtros['page'] - 1])); ?>">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Páginas numeradas -->
                        <?php
                        $start_page = max(1, $filtros['page'] - 2);
                        $end_page = min($total_pages, $filtros['page'] + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i == $filtros['page'] ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <!-- Última página -->
                        <?php if ($filtros['page'] < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['page' => $filtros['page'] + 1])); ?>">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['page' => $total_pages])); ?>">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Página <?php echo $filtros['page']; ?> de <?php echo $total_pages; ?>
                            (<?php echo number_format($total_pas); ?> PAs • <?php echo number_format($total_colaboradores); ?> colaboradores)
                        </small>
                    </div>
                </nav>
                <?php endif; ?>
                </div>
            </div>


    </div>

    <!-- Modal de Detalhes do Colaborador -->
    <div class="modal fade" id="detalhesModal" tabindex="-1" aria-labelledby="detalhesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detalhesModalLabel">
                        <i class="fas fa-user me-2"></i>Detalhes do Colaborador
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detalhesContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando detalhes...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Envio de Email -->
    <div class="modal fade" id="envioEmailModal" tabindex="-1" aria-labelledby="envioEmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="envioEmailModalLabel">
                        <i class="fas fa-envelope me-2"></i>Enviar Email para <span id="nome_destinatario"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formEnvioEmail">
                        <input type="hidden" id="cpf_colaborador_email" value="">

                        <!-- Informações do Destinatário -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-user me-2"></i>Destinatário:</h6>
                            <div id="info_destinatario">
                                <!-- Será preenchido dinamicamente -->
                            </div>
                        </div>

                        <!-- Seleção de Template -->
                        <div class="mb-3">
                            <label for="template_email" class="form-label">
                                <i class="fas fa-file-alt me-1"></i>Selecione o Template de Email
                            </label>
                            <select class="form-select" id="template_email" required>
                                <option value="">Carregando templates...</option>
                            </select>
                        </div>

                        <!-- Opção de Envio -->
                        <div class="mb-3">
                            <label for="tipo_envio" class="form-label">
                                <i class="fas fa-paper-plane me-1"></i>Tipo de Envio
                            </label>
                            <select class="form-select" id="tipo_envio" required>
                                <option value="imediato">🚀 Envio Imediato (enviar agora)</option>
                                <option value="fila">⏰ Agendar na Fila (enviar posteriormente)</option>
                            </select>
                            <div class="form-text">
                                <small>
                                    <strong>Envio Imediato:</strong> O email será enviado instantaneamente.<br>
                                    <strong>Agendar na Fila:</strong> O email será adicionado à fila para processamento posterior.
                                </small>
                            </div>
                        </div>

                        <!-- Preview do Template -->
                        <div class="mb-3" id="preview_container" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-eye me-1"></i>Preview do Email
                            </label>
                            <div class="border rounded p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                                <div id="preview_content">
                                    <!-- Preview será carregado aqui -->
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="btn_enviar_email" disabled>
                        <i class="fas fa-paper-plane me-1"></i><span id="texto_botao_envio">Enviar Email</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Máscara para CPF
        document.getElementById('cpf').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                e.target.value = value;
            }
        });

        // Variável global para armazenar dados do colaborador atual
        let colaboradorAtual = null;

        // Função para ver detalhes do colaborador
        function verDetalhes(cpf) {
            const modal = new bootstrap.Modal(document.getElementById('detalhesModal'));
            const content = document.getElementById('detalhesContent');

            // Mostrar loading
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando detalhes...</p>
                </div>
            `;

            modal.show();

            // Carregar detalhes via AJAX
            fetch('detalhes_colaborador.php?cpf=' + encodeURIComponent(cpf))
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;

                    // Buscar dados do colaborador para uso no envio de email
                    buscarDadosColaborador(cpf);
                })
                .catch(error => {
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar detalhes: ${error.message}
                        </div>
                    `;
                });
        }

        // Função para buscar dados do colaborador para email
        function buscarDadosColaborador(cpf) {
            fetch('ajax_colaborador_dados.php?cpf=' + encodeURIComponent(cpf))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        colaboradorAtual = data.colaborador;
                    }
                })
                .catch(error => {
                    console.error('Erro ao buscar dados do colaborador:', error);
                });
        }

        // Função para abrir modal de envio de email
        function abrirModalEnvioEmail() {
            if (!colaboradorAtual) {
                alert('Dados do colaborador não carregados. Tente novamente.');
                return;
            }

            // Verificar se o colaborador tem email
            if (!colaboradorAtual.email || colaboradorAtual.email.trim() === '') {
                alert('Este colaborador não possui email cadastrado. Não é possível enviar email.');
                return;
            }

            // Preencher informações do destinatário
            document.getElementById('nome_destinatario').textContent = colaboradorAtual.nome;
            document.getElementById('cpf_colaborador_email').value = colaboradorAtual.cpf;
            document.getElementById('info_destinatario').innerHTML = `
                <strong>${colaboradorAtual.nome}</strong><br>
                <small class="text-muted">
                    CPF: ${formatarCpf(colaboradorAtual.cpf)} |
                    Email: ${colaboradorAtual.email}
                </small>
            `;

            const modal = new bootstrap.Modal(document.getElementById('envioEmailModal'));

            // Carregar templates disponíveis
            carregarTemplatesEmail();

            modal.show();
        }

        // Função para formatar CPF
        function formatarCpf(cpf) {
            if (cpf && cpf.length === 11) {
                return cpf.substr(0, 3) + '.' + cpf.substr(3, 3) + '.' + cpf.substr(6, 3) + '-' + cpf.substr(9, 2);
            }
            return cpf;
        }

        // Função para carregar templates de email
        function carregarTemplatesEmail() {
            const selectTemplate = document.getElementById('template_email');

            selectTemplate.innerHTML = '<option value="">Carregando templates...</option>';

            fetch('ajax_templates_list.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        selectTemplate.innerHTML = '<option value="">Selecione um template...</option>';

                        data.templates.forEach(template => {
                            const option = document.createElement('option');
                            option.value = template.id;
                            option.textContent = `${template.nome} (${template.tipo})`;
                            selectTemplate.appendChild(option);
                        });
                    } else {
                        selectTemplate.innerHTML = '<option value="">Erro ao carregar templates</option>';
                        console.error('Erro ao carregar templates:', data.error);
                    }
                })
                .catch(error => {
                    selectTemplate.innerHTML = '<option value="">Erro ao carregar templates</option>';
                    console.error('Erro:', error);
                });
        }



        // Função para limpar filtros
        function limparFiltros() {
            window.location.href = window.location.pathname;
        }

        // Função para exportar dados
        function exportarDados(tipo) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'excel');
            params.set('tipo', tipo || 'colaboradores');
            window.location.href = 'exportar_colaboradores.php?' + params.toString();
        }

        // Função para ver detalhes do curso
        function verDetalhesCurso(codigoCurso) {
            const modal = new bootstrap.Modal(document.getElementById('detalhesModal'));
            const content = document.getElementById('detalhesContent');

            // Mostrar loading
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando detalhes do curso...</p>
                </div>
            `;

            modal.show();

            // Carregar detalhes via AJAX
            fetch('detalhes_curso.php?codigo=' + encodeURIComponent(codigoCurso))
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar detalhes: ${error.message}
                        </div>
                    `;
                });
        }

        // Função para controlar o ícone do colapso dos filtros
        function toggleFilterIcon() {
            const collapseElement = document.getElementById('filtrosCollapse');
            const icon = document.querySelector('[data-bs-target="#filtrosCollapse"] i.fa-chevron-down');

            if (collapseElement && icon) {
                collapseElement.addEventListener('shown.bs.collapse', function () {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                });

                collapseElement.addEventListener('hidden.bs.collapse', function () {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                });
            }
        }

        // Auto-submit do formulário quando mudar filtros (opcional)
        document.querySelectorAll('#filtroForm select').forEach(select => {
            select.addEventListener('change', function() {
                // Opcional: submeter automaticamente quando mudar select
                // document.getElementById('filtroForm').submit();
            });
        });

        // Tooltip para badges de status
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Animação de entrada dos cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Aplicar animação aos cards
        document.querySelectorAll('.colaborador-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Função para atualizar estatísticas em tempo real (se necessário)
        function atualizarEstatisticas() {
            // Implementar se necessário atualização automática
        }

        // Função para controlar seções de PA
        function initPASections() {
            document.querySelectorAll('.pa-header').forEach(header => {
                header.addEventListener('click', function(e) {
                    e.preventDefault();

                    const target = this.getAttribute('data-bs-target');
                    const content = document.querySelector(target);
                    const toggle = this.querySelector('.pa-toggle');

                    // Verificar estado atual baseado na visibilidade real
                    const isVisible = content.style.display !== 'none' && !content.classList.contains('d-none');

                    if (isVisible) {
                        // Retrair
                        content.style.display = 'none';
                        content.classList.add('d-none');
                        content.classList.remove('show');
                        toggle.classList.add('collapsed');
                        this.classList.add('collapsed');
                        this.setAttribute('aria-expanded', 'false');
                    } else {
                        // Expandir
                        content.style.display = 'block';
                        content.classList.remove('d-none');
                        content.classList.add('show');
                        toggle.classList.remove('collapsed');
                        this.classList.remove('collapsed');
                        this.setAttribute('aria-expanded', 'true');
                    }
                });
            });
        }

        // Função para expandir/retrair todas as seções
        function toggleAllPASections(expand = true) {
            document.querySelectorAll('.pa-header').forEach(header => {
                const target = header.getAttribute('data-bs-target');
                const content = document.querySelector(target);
                const toggle = header.querySelector('.pa-toggle');

                if (expand) {
                    // Expandir
                    content.style.display = 'block';
                    content.classList.remove('d-none');
                    content.classList.add('show');
                    toggle.classList.remove('collapsed');
                    header.classList.remove('collapsed');
                    header.setAttribute('aria-expanded', 'true');
                } else {
                    // Retrair
                    content.style.display = 'none';
                    content.classList.add('d-none');
                    content.classList.remove('show');
                    toggle.classList.add('collapsed');
                    header.classList.add('collapsed');
                    header.setAttribute('aria-expanded', 'false');
                }
            });
        }

        // Função para melhorar usabilidade do filtro múltiplo de status
        function initMultiSelectStatus() {
            const statusSelect = document.getElementById('status_curso');
            if (!statusSelect) return;

            // Adicionar indicador visual de seleções
            function updateStatusIndicator() {
                const selected = Array.from(statusSelect.selectedOptions);
                const label = statusSelect.closest('.mb-3').querySelector('label');

                if (selected.length > 0) {
                    const statusTexts = selected.map(option => option.textContent.trim());
                    label.innerHTML = `Status <small class="text-primary">(${selected.length} selecionados)</small>`;
                    statusSelect.style.borderColor = '#0d6efd';
                    statusSelect.style.borderWidth = '2px';
                } else {
                    label.innerHTML = `Status <small class="text-muted">(múltipla seleção)</small>`;
                    statusSelect.style.borderColor = '';
                    statusSelect.style.borderWidth = '';
                }
            }

            // Atualizar indicador quando seleção mudar
            statusSelect.addEventListener('change', updateStatusIndicator);

            // Inicializar indicador
            updateStatusIndicator();

            // Adicionar tooltip explicativo
            statusSelect.setAttribute('title', 'Segure Ctrl e clique para selecionar múltiplos status. O colaborador deve ter cursos com TODOS os status selecionados para aparecer nos resultados.');
        }

        // Função para limpar filtros com confirmação
        function limparFiltrosComConfirmacao() {
            const statusSelect = document.getElementById('status_curso');
            const hasMultipleSelections = statusSelect && statusSelect.selectedOptions.length > 1;

            if (hasMultipleSelections) {
                if (confirm('Você tem múltiplos status selecionados. Deseja realmente limpar todos os filtros?')) {
                    limparFiltros();
                }
            } else {
                limparFiltros();
            }
        }

        // Função para toggle dos filtros avançados
        function toggleFiltrosAvancados() {
            const segundaLinha = document.querySelector('.filter-section .row:nth-child(3)');
            const botao = event.target.closest('button');

            if (segundaLinha.style.display === 'none') {
                segundaLinha.style.display = 'flex';
                botao.innerHTML = '<i class="fas fa-eye-slash me-2"></i>Ocultar Avançados';
                // Animar a entrada
                segundaLinha.style.opacity = '0';
                setTimeout(() => {
                    segundaLinha.style.transition = 'opacity 0.3s ease';
                    segundaLinha.style.opacity = '1';
                }, 10);
            } else {
                segundaLinha.style.transition = 'opacity 0.3s ease';
                segundaLinha.style.opacity = '0';
                setTimeout(() => {
                    segundaLinha.style.display = 'none';
                    botao.innerHTML = '<i class="fas fa-cog me-2"></i>Filtros Avançados';
                }, 300);
            }
        }

        // Inicializar event listeners para modal de email
        function initEmailModal() {
            const templateSelect = document.getElementById('template_email');
            const previewContainer = document.getElementById('preview_container');
            const previewContent = document.getElementById('preview_content');
            const btnEnviar = document.getElementById('btn_enviar_email');
            const tipoEnvioSelect = document.getElementById('tipo_envio');
            const textoBotao = document.getElementById('texto_botao_envio');

            templateSelect.addEventListener('change', function() {
                const templateId = this.value;

                if (templateId && colaboradorAtual) {
                    // Mostrar preview
                    previewContainer.style.display = 'block';
                    previewContent.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Carregando preview...</div>';

                    // Carregar preview do template
                    fetch(`ajax_template_preview.php?template_id=${templateId}&cpf=${colaboradorAtual.cpf}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                previewContent.innerHTML = `
                                    <div class="mb-2">
                                        <strong>Assunto:</strong> ${data.preview.assunto}
                                    </div>
                                    <div class="border-top pt-2">
                                        <iframe srcdoc="${data.preview.corpo_html.replace(/"/g, '&quot;')}"
                                                style="width: 100%; height: 200px; border: none;"></iframe>
                                    </div>
                                `;
                                btnEnviar.disabled = false;
                            } else {
                                previewContent.innerHTML = `<div class="alert alert-danger">Erro ao carregar preview: ${data.error}</div>`;
                                btnEnviar.disabled = true;
                            }
                        })
                        .catch(error => {
                            previewContent.innerHTML = `<div class="alert alert-danger">Erro ao carregar preview: ${error.message}</div>`;
                            btnEnviar.disabled = true;
                        });
                } else {
                    previewContainer.style.display = 'none';
                    btnEnviar.disabled = true;
                }
            });

            // Event listener para mudança do tipo de envio
            tipoEnvioSelect.addEventListener('change', function() {
                const tipoEnvio = this.value;
                if (tipoEnvio === 'imediato') {
                    textoBotao.textContent = 'Enviar Agora';
                    btnEnviar.className = 'btn btn-success';
                } else {
                    textoBotao.textContent = 'Agendar Envio';
                    btnEnviar.className = 'btn btn-primary';
                }
            });

            // Event listener para envio do email
            btnEnviar.addEventListener('click', function() {
                const templateId = templateSelect.value;
                const cpf = document.getElementById('cpf_colaborador_email').value;
                const tipoEnvio = document.getElementById('tipo_envio').value;

                if (!templateId) {
                    alert('Por favor, selecione um template de email.');
                    return;
                }

                if (!tipoEnvio) {
                    alert('Por favor, selecione o tipo de envio.');
                    return;
                }

                // Desabilitar botão durante o envio
                this.disabled = true;
                const textoOriginal = textoBotao.textContent;
                textoBotao.innerHTML = tipoEnvio === 'imediato' ?
                    '<i class="fas fa-spinner fa-spin me-1"></i>Enviando...' :
                    '<i class="fas fa-spinner fa-spin me-1"></i>Agendando...';

                // Enviar email
                const formData = new FormData();
                formData.append('acao', tipoEnvio === 'imediato' ? 'envio_individual_imediato' : 'envio_individual_modal');
                formData.append('template_id', templateId);
                formData.append('cpf_colaborador', cpf);

                fetch('emails.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erro na requisição: ' + response.status);
                    }
                    return response.text();
                })
                .then(data => {
                    // Verificar se houve sucesso (procurar por mensagem de sucesso na resposta HTML)
                    if (data.includes('alert-success') || data.includes('sucesso')) {
                        let mensagem = '';
                        if (tipoEnvio === 'imediato') {
                            if (data.includes('enviado com sucesso')) {
                                mensagem = 'Email enviado com sucesso!';
                            } else {
                                mensagem = 'Email processado com sucesso!';
                            }
                        } else {
                            mensagem = 'Email agendado com sucesso! O email será enviado em breve.';
                        }
                        alert(mensagem);

                        // Fechar modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('envioEmailModal'));
                        modal.hide();

                        // Limpar formulário
                        templateSelect.value = '';
                        document.getElementById('tipo_envio').value = 'imediato';
                        previewContainer.style.display = 'none';
                    } else if (data.includes('alert-danger') || data.includes('Erro')) {
                        // Extrair mensagem de erro se possível
                        const errorMatch = data.match(/alert-danger[^>]*>([^<]+)/);
                        const errorMessage = errorMatch ? errorMatch[1].trim() : 'Erro ao processar email. Tente novamente.';
                        alert(errorMessage);
                    } else {
                        alert('Resposta inesperada do servidor. Tente novamente.');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao enviar email: ' + error.message);
                })
                .finally(() => {
                    // Reabilitar botão
                    this.disabled = false;
                    textoBotao.textContent = textoOriginal;
                });
            });
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar controle do ícone dos filtros
            toggleFilterIcon();

            // Inicializar seções de PA
            initPASections();

            // Inicializar filtro múltiplo de status
            initMultiSelectStatus();

            // Inicializar modal de email
            initEmailModal();

            // Inicializar filtros avançados como ocultos se não houver filtros aplicados
            const temFiltrosAvancados = document.getElementById('status_curso').selectedOptions.length > 0 ||
                                       document.getElementById('periodo_prazo').value;

            if (!temFiltrosAvancados) {
                const segundaLinha = document.querySelector('.filter-section .row:nth-child(3)');
                if (segundaLinha) {
                    segundaLinha.style.display = 'none';
                    // Atualizar texto do botão
                    const botaoAvancados = document.querySelector('button[onclick="toggleFiltrosAvancados()"]');
                    if (botaoAvancados) {
                        botaoAvancados.innerHTML = '<i class="fas fa-cog me-2"></i>Filtros Avançados';
                    }
                }
            }

            // Focar no primeiro campo do formulário se não houver filtros
            const urlParams = new URLSearchParams(window.location.search);

            if (!urlParams.toString() || urlParams.toString() === 'aba=colaboradores') {
                const cpfField = document.getElementById('cpf');
                if (cpfField) cpfField.focus();
            }
        });
    </script>
</body>
</html>
