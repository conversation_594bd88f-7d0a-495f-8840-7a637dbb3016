<?php
// Versão super simplificada para testar
error_reporting(0);
ini_set('display_errors', 0);

// Evitar qualquer output antes do JSON
ob_start();

try {
    require_once 'config/database.php';

    // Limpar qualquer output anterior
    ob_clean();
    header('Content-Type: application/json');

    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);

    if ($limit <= 0 || $limit > 100) {
        $limit = 20;
    }

    // Query básica sem filtros da Intranet
    $params = [];
    $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];

    if (!empty($search)) {
        $searchTerm = "%$search%";
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    $sql = "
        SELECT
            cpf,
            MAX(usuario) as nome,
            MAX(email) as email,
            MAX(funcao) as funcao,
            MAX(codigo_unidade) as codigo_unidade
        FROM edu_relatorio_educacao
        WHERE " . implode(" AND ", $whereConditions) . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
        LIMIT " . $limit;

    $stmt = $pdo_edu->prepare($sql);
    $stmt->execute($params);
    $colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatar dados para retorno
    $resultado = [];
    foreach ($colaboradores as $colaborador) {
        $resultado[] = [
            'cpf' => $colaborador['cpf'],
            'nome' => $colaborador['nome'] ?: 'Nome não informado',
            'email' => $colaborador['email'] ?: 'E-mail não informado',
            'funcao' => $colaborador['funcao'] ?: 'Função não informada',
            'pa' => extrairPA($colaborador['codigo_unidade'] ?? ''),
            'cursos_a_vencer' => 0,
            'cursos_vencidos' => 0,
            'cpf_formatado' => formatarCpf($colaborador['cpf'])
        ];
    }
    
    ob_clean();
    
    echo json_encode([
        'success' => true,
        'colaboradores' => $resultado,
        'total' => count($resultado),
        'search' => $search
    ]);
    
} catch (Exception $e) {
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

ob_end_flush();

function formatarCpf($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

function extrairPA($codigo_unidade) {
    if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
        return $matches[1] . ' - ' . $matches[2];
    }
    return $codigo_unidade ?: 'N/A';
}
?>
