<?php
// Relatório Completo de Colaboradores
// Este arquivo gera um relatório detalhado de todos os colaboradores com seus cursos

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(hierarquia_unidade) as hierarquia_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_aprovados,
        COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$colaboradores = $stmt_colaboradores->fetchAll();

// APLICAR FILTRO DE USUÁRIOS INATIVOS DA INTRANET
if (function_exists('aplicarFiltroUsuariosInativos')) {
    $colaboradores = aplicarFiltroUsuariosInativos($colaboradores);
}

// Filtrar por período se especificado
if (!empty($filtros['periodo'])) {
    $colaboradores_filtrados = [];
    
    foreach ($colaboradores as $colaborador) {
        $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
        $tem_curso_no_periodo = false;
        
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
        
        if ($tem_curso_no_periodo) {
            $colaboradores_filtrados[] = $colaborador;
        }
    }
    
    $colaboradores = $colaboradores_filtrados;
}

// Filtrar por PA se especificado
if (!empty($filtros['pa'])) {
    $colaboradores_filtrados = [];
    
    foreach ($colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
        
        if ($usuario_intranet && $usuario_intranet['agencia'] == $filtros['pa']) {
            $colaboradores_filtrados[] = $colaborador;
        }
    }
    
    $colaboradores = $colaboradores_filtrados;
}

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #003641; color: white; font-weight: bold;">';
echo '<td colspan="21" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO COMPLETO DE COLABORADORES - EDUCAÇÃO CORPORATIVA';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="20" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="20" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ? 
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] : 
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Colaboradores:</td>';
echo '<td colspan="20" style="padding: 8px;">' . count($colaboradores) . '</td>';
echo '</tr>';

echo '<tr><td colspan="21" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Data Admissão</td>';
echo '<td style="padding: 8px; text-align: center;">Total Trilhas</td>';
echo '<td style="padding: 8px; text-align: center;">Total Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos Concluídos</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos Em Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">% Conclusão</td>';
echo '<td style="padding: 8px; text-align: center;">Média Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Status Geral</td>';
echo '<td style="padding: 8px; text-align: center;">Última Atualização</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha Obrigatória</td>';
echo '<td style="padding: 8px; text-align: center;">Curso</td>';
echo '</tr>';

// Dados dos colaboradores
$linha = 0;
foreach ($colaboradores as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Buscar cursos do colaborador
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    
    // Calcular métricas
    $cursos_vencidos = 0;
    $cursos_a_vencer = 0;
    $cursos_em_andamento = 0;
    
    foreach ($cursos_colaborador as $curso) {
        if ($curso['status_prazo'] === 'vencido') {
            $cursos_vencidos++;
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $cursos_a_vencer++;
        } elseif (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
            $cursos_em_andamento++;
        }
    }
    
    // Determinar status geral
    $status_geral = 'Em Dia';
    if ($cursos_vencidos > 0) {
        $status_geral = 'Cursos Vencidos';
    } elseif ($cursos_a_vencer > 0) {
        $status_geral = 'A Vencer';
    } elseif ($cursos_em_andamento > 0) {
        $status_geral = 'Em Andamento';
    }
    
    // Calcular percentual de conclusão
    $percentual_conclusao = $colaborador['total_cursos'] > 0 ? 
        ($colaborador['cursos_concluidos'] / $colaborador['total_cursos']) * 100 : 0;
    
    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';
    
    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }
    
    // Se há cursos, criar uma linha para cada curso
    if (!empty($cursos_colaborador)) {
        $primeira_linha = true;
        foreach ($cursos_colaborador as $curso) {
            $cor_linha = ($linha % 2 == 0) ? '#f8f9fa' : '#ffffff';
            echo '<tr style="background-color: ' . $cor_linha . ';">';
            
            if ($primeira_linha) {
                // Dados do colaborador (apenas na primeira linha)
                echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
                echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
                echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
                echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
                echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
                echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
                echo '<td style="padding: 6px;">' . ($colaborador['data_admissao'] ? date('d/m/Y', strtotime($colaborador['data_admissao'])) : 'N/A') . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $colaborador['total_trilhas'] . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $colaborador['total_cursos'] . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $colaborador['cursos_aprovados'] . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $colaborador['cursos_concluidos'] . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $cursos_vencidos . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $cursos_a_vencer . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . $cursos_em_andamento . '</td>';
                echo '<td style="padding: 6px; text-align: center;">' . number_format($percentual_conclusao, 1) . '%</td>';
                echo '<td style="padding: 6px; text-align: center;">' . ($colaborador['media_aproveitamento'] ? number_format($colaborador['media_aproveitamento'], 1) . '%' : 'N/A') . '</td>';
                echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . $status_geral . '</td>';
                echo '<td style="padding: 6px;">' . date('d/m/Y', strtotime($colaborador['ultima_atualizacao'])) . '</td>';
                $primeira_linha = false;
            } else {
                // Células vazias para as outras linhas do mesmo colaborador
                echo '<td colspan="18" style="padding: 6px; border-left: none;"></td>';
            }

            // Dados do curso
            echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';

            // Verificar se a trilha é obrigatória
            $trilha_obrigatoria = isTrilhaObrigatoria($curso['codigo_trilha'], $trilhas_obrigatorias);
            echo '<td style="padding: 6px; text-align: center;">';
            if ($trilha_obrigatoria) {
                echo '<span style="color: #856404; font-weight: bold;">SIM</span>';
            } else {
                echo '<span style="color: #6c757d;">NÃO</span>';
            }
            echo '</td>';

            echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
            echo '</tr>';
            
            // Linha adicional com detalhes do curso
            echo '<tr style="background-color: ' . $cor_linha . ';">';
            echo '<td colspan="18" style="padding: 4px; font-size: 11px; color: #666; border-top: none;"></td>';
            echo '<td colspan="3" style="padding: 4px; font-size: 11px; color: #666; border-top: none;">';
            echo 'Status: ' . ucfirst(str_replace('_', ' ', $curso['status_prazo']));
            if ($curso['aprovacao'] === 'Sim') {
                echo ' | Aprovado';
                if ($curso['nota_recurso']) echo ' | Nota: ' . $curso['nota_recurso'];
            } elseif (!empty($curso['andamento_etapa'])) {
                echo ' | Em andamento: ' . $curso['andamento_etapa'];
            }
            if ($curso['prazo_calculado']) {
                echo ' | Prazo: ' . date('d/m/Y', strtotime($curso['prazo_calculado']));
                if ($curso['dias_prazo'] !== null) {
                    if ($curso['dias_prazo'] > 0) {
                        echo ' (' . $curso['dias_prazo'] . ' dias restantes)';
                    } else {
                        echo ' (' . abs($curso['dias_prazo']) . ' dias em atraso)';
                    }
                }
            }
            echo '</td>';
            echo '</tr>';
        }
    } else {
        // Colaborador sem cursos
        $cor_linha = ($linha % 2 == 0) ? '#f8f9fa' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';
        echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . ($colaborador['data_admissao'] ? date('d/m/Y', strtotime($colaborador['data_admissao'])) : 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0%</td>';
        echo '<td style="padding: 6px; text-align: center;">N/A</td>';
        echo '<td style="padding: 6px; text-align: center;">Sem Cursos</td>';
        echo '<td style="padding: 6px;">' . date('d/m/Y', strtotime($colaborador['ultima_atualizacao'])) . '</td>';
        echo '<td style="padding: 6px; color: #999;">Nenhum curso atribuído</td>';
        echo '<td style="padding: 6px; color: #999;">-</td>';
        echo '<td style="padding: 6px; color: #999;">-</td>';
        echo '</tr>';
    }
}

echo '</table>';
?>
