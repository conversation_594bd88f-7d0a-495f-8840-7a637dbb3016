<?php
/**
 * <PERSON>ript para verificar as colunas da tabela edu_relatorio_educacao
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h1>🔍 Verificar Colunas da Tabela</h1>\n";

try {
    // Verificar estrutura da tabela
    $query = "DESCRIBE edu_relatorio_educacao";
    $stmt = $pdo_edu->prepare($query);
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📋 Estrutura da Tabela edu_relatorio_educacao:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px;'>Campo</th>";
    echo "<th style='padding: 10px;'>Tipo</th>";
    echo "<th style='padding: 10px;'>Nulo</th>";
    echo "<th style='padding: 10px;'>Chave</th>";
    echo "<th style='padding: 10px;'>Padrão</th>";
    echo "</tr>";
    
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>" . htmlspecialchars($coluna['Field']) . "</strong></td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($coluna['Type']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($coluna['Null']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($coluna['Key']) . "</td>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    // Buscar colunas relacionadas a prazo/data
    $colunas_prazo = [];
    $colunas_data = [];
    
    foreach ($colunas as $coluna) {
        $nome_campo = strtolower($coluna['Field']);
        if (strpos($nome_campo, 'prazo') !== false) {
            $colunas_prazo[] = $coluna['Field'];
        }
        if (strpos($nome_campo, 'data') !== false || strpos($nome_campo, 'date') !== false) {
            $colunas_data[] = $coluna['Field'];
        }
    }
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📅 Colunas Relacionadas a Prazo:</h3>";
    if (empty($colunas_prazo)) {
        echo "<p><strong>❌ Nenhuma coluna com 'prazo' encontrada!</strong></p>";
    } else {
        echo "<ul>";
        foreach ($colunas_prazo as $coluna) {
            echo "<li><strong>$coluna</strong></li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📅 Colunas Relacionadas a Data:</h3>";
    if (empty($colunas_data)) {
        echo "<p><strong>❌ Nenhuma coluna com 'data' encontrada!</strong></p>";
    } else {
        echo "<ul>";
        foreach ($colunas_data as $coluna) {
            echo "<li><strong>$coluna</strong></li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    // Buscar amostra de dados para entender a estrutura
    echo "<h2>📋 Amostra de Dados</h2>\n";
    
    $query_amostra = "SELECT * FROM edu_relatorio_educacao LIMIT 3";
    $stmt_amostra = $pdo_edu->prepare($query_amostra);
    $stmt_amostra->execute();
    $amostra = $stmt_amostra->fetchAll();
    
    if (!empty($amostra)) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📋 Primeiros 3 Registros:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        
        // Cabeçalho
        echo "<tr style='background: #e9ecef;'>";
        foreach (array_keys($amostra[0]) as $coluna) {
            echo "<th style='padding: 5px; max-width: 100px; word-wrap: break-word;'>" . htmlspecialchars($coluna) . "</th>";
        }
        echo "</tr>";
        
        // Dados
        foreach ($amostra as $registro) {
            echo "<tr>";
            foreach ($registro as $valor) {
                $valor_exibir = is_null($valor) ? 'NULL' : htmlspecialchars(substr($valor, 0, 50));
                if (strlen($valor) > 50) $valor_exibir .= '...';
                echo "<td style='padding: 5px; max-width: 100px; word-wrap: break-word;'>$valor_exibir</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
    // Sugestões de correção
    echo "<h2>🔧 Sugestões de Correção</h2>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ Possíveis Nomes de Colunas para Prazo:</h3>";
    
    $possiveis_colunas_prazo = [];
    foreach ($colunas as $coluna) {
        $nome_campo = strtolower($coluna['Field']);
        if (strpos($nome_campo, 'prazo') !== false || 
            strpos($nome_campo, 'vencimento') !== false ||
            strpos($nome_campo, 'limite') !== false ||
            strpos($nome_campo, 'deadline') !== false) {
            $possiveis_colunas_prazo[] = $coluna['Field'];
        }
    }
    
    if (empty($possiveis_colunas_prazo)) {
        echo "<p><strong>❌ Nenhuma coluna de prazo encontrada!</strong></p>";
        echo "<p>Verifique se existe uma coluna que representa o prazo de conclusão dos cursos.</p>";
    } else {
        echo "<ul>";
        foreach ($possiveis_colunas_prazo as $coluna) {
            echo "<li><strong>$coluna</strong> - Pode ser usada para calcular cursos vencidos</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro ao verificar tabela:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<p><em>Verificação executada em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
