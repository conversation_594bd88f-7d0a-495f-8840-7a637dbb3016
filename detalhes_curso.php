<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/IntranetAPI.php';

// Verificar se código do curso foi fornecido
if (empty($_GET['codigo'])) {
    echo '<div class="alert alert-danger">Código do curso não fornecido.</div>';
    exit;
}

$codigo_curso = $_GET['codigo'];

// Inicializar API da Intranet
$api = new IntranetAPI();

// Buscar dados do curso
$query_curso = "
    SELECT 
        codigo_recurso,
        recurso,
        trilha,
        codigo_trilha,
        carga_horaria_recurso,
        COUNT(DISTINCT cpf) as total_colaboradores,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
        COUNT(*) as total_registros,
        AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
        MAX(data_importacao) as ultima_atualizacao,
        MIN(data_conclusao) as primeira_conclusao,
        MAX(data_conclusao) as ultima_conclusao
    FROM edu_relatorio_educacao
    WHERE codigo_recurso = ?
    GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
";

$stmt = $pdo_edu->prepare($query_curso);
$stmt->execute([$codigo_curso]);
$curso = $stmt->fetch();

if (!$curso) {
    echo '<div class="alert alert-warning">Curso não encontrado.</div>';
    exit;
}

// Buscar colaboradores do curso
$query_colaboradores = "
    SELECT 
        cpf,
        usuario,
        aprovacao,
        nota_recurso,
        aproveitamento,
        data_conclusao,
        validade_recurso,
        andamento_etapa,
        situacao_trilha
    FROM edu_relatorio_educacao
    WHERE codigo_recurso = ?
    ORDER BY usuario
";

$stmt_colaboradores = $pdo_edu->prepare($query_colaboradores);
$stmt_colaboradores->execute([$codigo_curso]);
$colaboradores = $stmt_colaboradores->fetchAll();

// Buscar dados da Intranet para enriquecimento
$usuarios_intranet = $api->listarUsuarios();
$mapa_usuarios_cpf = [];
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
}

// Função para formatar data
function formatarData($data) {
    if (empty($data) || $data === '0000-00-00') return 'N/A';
    return date('d/m/Y', strtotime($data));
}

// Função para calcular status do colaborador no curso
function getStatusColaboradorCurso($colaborador) {
    if ($colaborador['aprovacao'] === 'Sim') {
        return ['classe' => 'success', 'texto' => 'Aprovado', 'icone' => 'fa-check-circle'];
    } elseif (!empty($colaborador['data_conclusao']) && $colaborador['data_conclusao'] !== '0000-00-00') {
        return ['classe' => 'warning', 'texto' => 'Concluído', 'icone' => 'fa-clock'];
    } elseif (!empty($colaborador['andamento_etapa'])) {
        return ['classe' => 'info', 'texto' => 'Em Andamento', 'icone' => 'fa-play-circle'];
    } else {
        return ['classe' => 'secondary', 'texto' => 'Pendente', 'icone' => 'fa-hourglass-half'];
    }
}

// Calcular estatísticas
$percentual_aprovacao = $curso['total_colaboradores'] > 0 ? 
    ($curso['total_aprovados'] / $curso['total_colaboradores']) * 100 : 0;
$percentual_conclusao = $curso['total_colaboradores'] > 0 ? 
    ($curso['total_concluidos'] / $curso['total_colaboradores']) * 100 : 0;
?>

<div class="container-fluid">
    <!-- Informações Básicas do Curso -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Informações do Curso</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Nome:</strong></td>
                            <td><?php echo htmlspecialchars($curso['recurso']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Código:</strong></td>
                            <td><?php echo htmlspecialchars($curso['codigo_recurso']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Trilha:</strong></td>
                            <td><?php echo htmlspecialchars($curso['trilha']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Código da Trilha:</strong></td>
                            <td><?php echo htmlspecialchars($curso['codigo_trilha']); ?></td>
                        </tr>
                        <?php if ($curso['carga_horaria_recurso']): ?>
                        <tr>
                            <td><strong>Carga Horária:</strong></td>
                            <td><?php echo htmlspecialchars($curso['carga_horaria_recurso']); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td><strong>Última Atualização:</strong></td>
                            <td><?php echo formatarData($curso['ultima_atualizacao']); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Estatísticas</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h4 class="text-primary"><?php echo $curso['total_colaboradores']; ?></h4>
                        <small class="text-muted">Total de Colaboradores</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <small>Taxa de Aprovação</small>
                            <small><?php echo number_format($percentual_aprovacao, 1); ?>%</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-success" style="width: <?php echo $percentual_aprovacao; ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <small>Taxa de Conclusão</small>
                            <small><?php echo number_format($percentual_conclusao, 1); ?>%</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: <?php echo $percentual_conclusao; ?>%"></div>
                        </div>
                    </div>
                    
                    <?php if ($curso['media_notas']): ?>
                    <div class="text-center">
                        <strong>Média de Notas:</strong> <?php echo number_format($curso['media_notas'], 1); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($curso['media_aproveitamento']): ?>
                    <div class="text-center">
                        <strong>Aproveitamento Médio:</strong> <?php echo number_format($curso['media_aproveitamento'], 1); ?>%
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumo de Performance -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success"><?php echo $curso['total_aprovados']; ?></h4>
                    <small class="text-muted">Aprovados</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info"><?php echo $curso['total_concluidos']; ?></h4>
                    <small class="text-muted">Concluídos</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning"><?php echo $curso['total_colaboradores'] - $curso['total_concluidos']; ?></h4>
                    <small class="text-muted">Pendentes</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary"><?php echo $curso['total_registros']; ?></h4>
                    <small class="text-muted">Total Registros</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Colaboradores -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-users me-2"></i>Colaboradores no Curso</h6>
        </div>
        <div class="card-body">
            <?php if (empty($colaboradores)): ?>
            <p class="text-muted">Nenhum colaborador encontrado para este curso.</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Colaborador</th>
                            <th>Status</th>
                            <th>Nota</th>
                            <th>Aproveitamento</th>
                            <th>Conclusão</th>
                            <th>Validade</th>
                            <th>Agência</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($colaboradores as $colaborador): ?>
                        <?php 
                            $status = getStatusColaboradorCurso($colaborador);
                            $usuario_intranet = $mapa_usuarios_cpf[$colaborador['cpf']] ?? null;
                        ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($colaborador['usuario']); ?></strong>
                                    <?php if ($usuario_intranet): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($usuario_intranet['nome'] ?? ''); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $status['classe']; ?>">
                                    <i class="fas <?php echo $status['icone']; ?> me-1"></i>
                                    <?php echo $status['texto']; ?>
                                </span>
                                <?php if ($colaborador['situacao_trilha']): ?>
                                <br><small class="text-muted">Trilha: <?php echo htmlspecialchars($colaborador['situacao_trilha']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($colaborador['nota_recurso']): ?>
                                    <span class="fw-bold"><?php echo number_format($colaborador['nota_recurso'], 1); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($colaborador['aproveitamento']): ?>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?php echo number_format($colaborador['aproveitamento'], 1); ?>%</span>
                                        <div class="progress flex-grow-1" style="height: 6px; width: 60px;">
                                            <div class="progress-bar bg-primary" 
                                                 style="width: <?php echo $colaborador['aproveitamento']; ?>%"></div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo formatarData($colaborador['data_conclusao']); ?></td>
                            <td><?php echo formatarData($colaborador['validade_recurso']); ?></td>
                            <td>
                                <?php if ($usuario_intranet): ?>
                                    <span class="badge bg-light text-dark">
                                        <?php echo htmlspecialchars($usuario_intranet['agencia'] ?? 'N/A'); ?>
                                    </span>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($usuario_intranet['nomeSetor'] ?? ''); ?></small>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
