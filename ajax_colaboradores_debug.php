<?php
// Versão de debug para testar a API de colaboradores
error_reporting(E_ALL);
ini_set('display_errors', 0); // Não exibir erros na tela para não quebrar JSON

// Evitar qualquer output antes do JSON
ob_start();

try {
    require_once 'edu_auth_check.php';
    require_once 'config/database.php';
    require_once 'classes/IntranetAPI.php';

    // Verificar permissões
    checkPageAccess(['gestor', 'admin']);

    // Limpar qualquer output anterior
    ob_clean();
    header('Content-Type: application/json');

    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);

    // Garantir que o limit seja um número válido
    if ($limit <= 0 || $limit > 100) {
        $limit = 20;
    }

    // Verificar se a tabela existe
    $stmt = $pdo_edu->query("SHOW TABLES LIKE 'edu_relatorio_educacao'");
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        throw new Exception("Tabela edu_relatorio_educacao não encontrada");
    }

    // Query simplificada SEM filtro da Intranet primeiro (para debug)
    $params = [];
    $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];

    if (!empty($search)) {
        $searchTerm = "%$search%";
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    $sql = "
        SELECT
            cpf,
            MAX(usuario) as nome,
            MAX(email) as email,
            MAX(funcao) as funcao,
            MAX(codigo_unidade) as codigo_unidade,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
        FROM edu_relatorio_educacao
        WHERE " . implode(" AND ", $whereConditions) . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
        LIMIT " . $limit;

    $stmt = $pdo_edu->prepare($sql);
    $stmt->execute($params);
    $todos_colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Inicializar mapas vazios para fallback
    $mapa_usuarios_ativos = [];
    $mapa_usuarios_todos = [];
    $api_error = null;
    
    // Tentar carregar API da Intranet
    try {
        $api = new IntranetAPI();
        $usuarios_intranet_todos = $api->listarUsuarios();
        $usuarios_intranet_ativos = $api->listarUsuariosAtivos();

        // Mapa de usuários ativos
        if ($usuarios_intranet_ativos !== false && is_array($usuarios_intranet_ativos)) {
            foreach ($usuarios_intranet_ativos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
                }
            }
        }

        // Mapa de todos os usuários
        if ($usuarios_intranet_todos !== false && is_array($usuarios_intranet_todos)) {
            foreach ($usuarios_intranet_todos as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
                }
            }
        }
    } catch (Exception $apiError) {
        $api_error = $apiError->getMessage();
        error_log("Erro na API da Intranet em ajax_colaboradores_debug.php: " . $api_error);
    }

    // Aplicar filtro de usuários inativos (se a API funcionou)
    $colaboradores_filtrados = [];
    $total_filtrados = 0;
    
    foreach ($todos_colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);

        $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;

        // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
        if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
            $total_filtrados++;
            continue; // Usuário inativo - PULAR
        }

        // Incluir colaborador na lista filtrada
        $colaboradores_filtrados[] = $colaborador;
    }

    // Formatar dados para retorno
    $resultado = [];
    foreach ($colaboradores_filtrados as $colaborador) {
        $resultado[] = [
            'cpf' => $colaborador['cpf'],
            'nome' => $colaborador['nome'] ?: 'Nome não informado',
            'email' => $colaborador['email'] ?: 'E-mail não informado',
            'funcao' => $colaborador['funcao'] ?: 'Função não informada',
            'pa' => extrairPA($colaborador['codigo_unidade'] ?? ''),
            'cursos_a_vencer' => (int)($colaborador['cursos_a_vencer'] ?? 0),
            'cursos_vencidos' => (int)($colaborador['cursos_vencidos'] ?? 0),
            'cpf_formatado' => formatarCpf($colaborador['cpf'])
        ];
    }
    
    // Limpar qualquer output indesejado antes de retornar JSON
    ob_clean();
    
    echo json_encode([
        'success' => true,
        'colaboradores' => $resultado,
        'total' => count($resultado),
        'debug' => [
            'total_banco' => count($todos_colaboradores),
            'total_filtrados' => $total_filtrados,
            'total_final' => count($colaboradores_filtrados),
            'api_ativos' => count($mapa_usuarios_ativos),
            'api_todos' => count($mapa_usuarios_todos),
            'api_error' => $api_error,
            'search_term' => $search
        ]
    ]);
    
} catch (Exception $e) {
    // Limpar qualquer output indesejado antes de retornar JSON de erro
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => __FILE__,
        'line' => $e->getLine()
    ]);
}

// Finalizar output buffer
ob_end_flush();

function formatarCpf($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

function extrairPA($codigo_unidade) {
    if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
        return $matches[1] . ' - ' . $matches[2];
    }
    return $codigo_unidade ?: 'N/A';
}
?>
