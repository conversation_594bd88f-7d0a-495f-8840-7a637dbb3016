<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';

// Verificar permissões
checkPageAccess(['gestor', 'admin']);

header('Content-Type: application/json');

try {
    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);

    // Garantir que o limit seja um número válido
    if ($limit <= 0 || $limit > 100) {
        $limit = 20;
    }

    // Verificar se a tabela existe
    $stmt = $pdo_edu->query("SHOW TABLES LIKE 'edu_relatorio_educacao'");
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        throw new Exception("Tabela edu_relatorio_educacao não encontrada");
    }

    // Query simplificada e unificada
    $params = [];
    $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];

    if (!empty($search)) {
        $searchTerm = "%$search%";
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    $sql = "
        SELECT
            cpf,
            MAX(usuario) as nome,
            MAX(email) as email,
            MAX(funcao) as funcao,
            MAX(codigo_unidade) as codigo_unidade,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
        FROM edu_relatorio_educacao
        WHERE " . implode(" AND ", $whereConditions) . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
        LIMIT " . $limit;

    $stmt = $pdo_edu->prepare($sql);
    $stmt->execute($params);
    $colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formatar dados para retorno
    $resultado = [];
    foreach ($colaboradores as $colaborador) {
        $resultado[] = [
            'cpf' => $colaborador['cpf'],
            'nome' => $colaborador['nome'] ?: 'Nome não informado',
            'email' => $colaborador['email'] ?: 'E-mail não informado',
            'funcao' => $colaborador['funcao'] ?: 'Função não informada',
            'pa' => extrairPA($colaborador['codigo_unidade'] ?? ''),
            'cursos_a_vencer' => (int)($colaborador['cursos_a_vencer'] ?? 0),
            'cursos_vencidos' => (int)($colaborador['cursos_vencidos'] ?? 0),
            'cpf_formatado' => formatarCpf($colaborador['cpf'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'colaboradores' => $resultado,
        'total' => count($resultado)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function formatarCpf($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

function extrairPA($codigo_unidade) {
    if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
        return $matches[1] . ' - ' . $matches[2];
    }
    return $codigo_unidade ?: 'N/A';
}
?>
