<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Verificar permissões
checkPageAccess(['gestor', 'admin']);

header('Content-Type: application/json');

try {
    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);

    // Garantir que o limit seja um número válido
    if ($limit <= 0 || $limit > 100) {
        $limit = 20;
    }

    // Inicializar API da Intranet para aplicar filtro de usuários inativos
    $api = new IntranetAPI();
    $usuarios_intranet_todos = $api->listarUsuarios(); // Todos os usuários
    $usuarios_intranet_ativos = $api->listarUsuariosAtivos(); // Apenas ativos

    // Criar mapas de usuários por CPF
    $mapa_usuarios_ativos = []; // Apenas usuários ativos
    $mapa_usuarios_todos = [];  // Todos os usuários (para verificar se existe na Intranet)

    // Mapa de usuários ativos
    if ($usuarios_intranet_ativos !== false) {
        foreach ($usuarios_intranet_ativos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
            }
        }
    }

    // Mapa de todos os usuários
    if ($usuarios_intranet_todos !== false) {
        foreach ($usuarios_intranet_todos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
            }
        }
    }

    // Verificar se a tabela existe
    $stmt = $pdo_edu->query("SHOW TABLES LIKE 'edu_relatorio_educacao'");
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        throw new Exception("Tabela edu_relatorio_educacao não encontrada");
    }

    // Query simplificada e unificada
    $params = [];
    $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];

    if (!empty($search)) {
        $searchTerm = "%$search%";
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    $sql = "
        SELECT
            cpf,
            MAX(usuario) as nome,
            MAX(email) as email,
            MAX(funcao) as funcao,
            MAX(codigo_unidade) as codigo_unidade,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
        FROM edu_relatorio_educacao
        WHERE " . implode(" AND ", $whereConditions) . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
        LIMIT " . $limit;

    $stmt = $pdo_edu->prepare($sql);
    $stmt->execute($params);
    $todos_colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // APLICAR FILTRO DE USUÁRIOS INATIVOS
    $colaboradores_filtrados = [];
    foreach ($todos_colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);

        $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;

        // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
        if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
            continue; // Usuário inativo - PULAR
        }

        // Incluir colaborador na lista filtrada
        $colaboradores_filtrados[] = $colaborador;
    }

    // Formatar dados para retorno
    $resultado = [];
    foreach ($colaboradores_filtrados as $colaborador) {
        $resultado[] = [
            'cpf' => $colaborador['cpf'],
            'nome' => $colaborador['nome'] ?: 'Nome não informado',
            'email' => $colaborador['email'] ?: 'E-mail não informado',
            'funcao' => $colaborador['funcao'] ?: 'Função não informada',
            'pa' => extrairPA($colaborador['codigo_unidade'] ?? ''),
            'cursos_a_vencer' => (int)($colaborador['cursos_a_vencer'] ?? 0),
            'cursos_vencidos' => (int)($colaborador['cursos_vencidos'] ?? 0),
            'cpf_formatado' => formatarCpf($colaborador['cpf'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'colaboradores' => $resultado,
        'total' => count($resultado)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function formatarCpf($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

function extrairPA($codigo_unidade) {
    if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
        return $matches[1] . ' - ' . $matches[2];
    }
    return $codigo_unidade ?: 'N/A';
}
?>
