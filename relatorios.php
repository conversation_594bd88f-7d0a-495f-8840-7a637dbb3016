<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/IntranetAPI.php';

// Verificar permissões (todos os níveis podem acessar)
checkEduPageAccess(['comum', 'gestor', 'admin']);

// Inicializar API da Intranet
$api = new IntranetAPI();

// Buscar dados da API da Intranet (com cache) - TODOS OS USUÁRIOS E APENAS ATIVOS
$usuarios_intranet_todos = $api->listarUsuarios(); // Todos os usuários
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(); // Apenas ativos
$agencias_intranet = $api->listarAgencias();

// Criar mapas de usuários por CPF
$mapa_usuarios_ativos = []; // Apenas usuários ativos
$mapa_usuarios_todos = [];  // Todos os usuários (para verificar se existe na Intranet)

// Mapa de usuários ativos (para exibição com dados enriquecidos)
if ($usuarios_intranet_ativos !== false) {
    foreach ($usuarios_intranet_ativos as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
        }
    }
}

// Mapa de todos os usuários (para verificar se existe na Intranet, mesmo que inativo)
if ($usuarios_intranet_todos !== false) {
    foreach ($usuarios_intranet_todos as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
        }
    }
}

// Para compatibilidade com o resto do código, usar o mapa de ativos como principal
$mapa_usuarios_cpf = $mapa_usuarios_ativos;
$usuarios_intranet = $usuarios_intranet_ativos;

// Criar mapa de agências
$mapa_agencias = [];
if ($agencias_intranet !== false) {
    foreach ($agencias_intranet as $agencia) {
        if (!empty($agencia['id'])) {
            $mapa_agencias[$agencia['id']] = $agencia;
        }
    }
}

// Buscar dados para os filtros
$trilhas_query = "SELECT DISTINCT trilha FROM edu_relatorio_educacao WHERE trilha IS NOT NULL AND trilha != '' ORDER BY trilha";
$trilhas_disponiveis = $pdo_edu->query($trilhas_query)->fetchAll(PDO::FETCH_COLUMN);

$cursos_query = "SELECT DISTINCT recurso FROM edu_relatorio_educacao WHERE recurso IS NOT NULL AND recurso != '' ORDER BY recurso";
$cursos_disponiveis = $pdo_edu->query($cursos_query)->fetchAll(PDO::FETCH_COLUMN);

// Buscar trilhas obrigatórias (com verificação se a tabela existe)
$trilhas_obrigatorias = [];
try {
    $trilhas_obrigatorias_query = "SELECT trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1 ORDER BY trilha";
    $trilhas_obrigatorias = $pdo_edu->query($trilhas_obrigatorias_query)->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // Tabela ainda não existe, continuar sem filtro de trilhas obrigatórias
    $trilhas_obrigatorias = [];
}

$funcoes_query = "SELECT DISTINCT funcao FROM edu_relatorio_educacao WHERE funcao IS NOT NULL AND funcao != '' ORDER BY funcao";
$funcoes_disponiveis = $pdo_edu->query($funcoes_query)->fetchAll(PDO::FETCH_COLUMN);

// Buscar estatísticas gerais - APLICANDO FILTRO DE USUÁRIOS INATIVOS
// Primeiro, buscar todos os colaboradores da base
$todos_colaboradores_query = "
    SELECT DISTINCT cpf
    FROM edu_relatorio_educacao
";
$stmt_todos_colaboradores = $pdo_edu->prepare($todos_colaboradores_query);
$stmt_todos_colaboradores->execute();
$todos_colaboradores_cpfs = $stmt_todos_colaboradores->fetchAll(PDO::FETCH_COLUMN);

// Aplicar filtro de usuários inativos
$colaboradores_validos = [];
foreach ($todos_colaboradores_cpfs as $cpf) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);

    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;

    // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
    if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
        continue; // Usuário inativo - PULAR
    }

    $colaboradores_validos[] = $cpf;
}

// Buscar estatísticas apenas dos colaboradores válidos (ativos + não encontrados na Intranet)
if (!empty($colaboradores_validos)) {
    $cpfs_placeholders = str_repeat('?,', count($colaboradores_validos) - 1) . '?';

    $stats_query = "
        SELECT
            COUNT(DISTINCT cpf) as total_colaboradores,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            COUNT(*) as total_registros,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento
        FROM edu_relatorio_educacao
        WHERE cpf IN ($cpfs_placeholders)
    ";

    $stmt_stats = $pdo_edu->prepare($stats_query);
    $stmt_stats->execute($colaboradores_validos);
    $estatisticas = $stmt_stats->fetch();
} else {
    // Se não há colaboradores válidos, zerar estatísticas
    $estatisticas = [
        'total_colaboradores' => 0,
        'total_trilhas' => 0,
        'total_cursos' => 0,
        'total_registros' => 0,
        'cursos_aprovados' => 0,
        'media_aproveitamento' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }



        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--sicoob-verde-claro), var(--sicoob-verde-medio));
            border: none;
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            font-weight: 600;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--sicoob-verde-claro), #ffc107);
            border: none;
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-medio));
            border: none;
            color: white;
            font-weight: 600;
        }

        .btn-outline-primary {
            border-color: var(--sicoob-turquesa);
            color: var(--sicoob-turquesa);
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--sicoob-cinza), #6c757d);
            border: none;
            color: white;
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0.6rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .report-section {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .report-option {
            background: var(--sicoob-branco);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .report-option:hover {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 2px 8px rgba(0, 174, 157, 0.1);
        }

        .stats-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }

        .stats-label {
            color: var(--sicoob-cinza);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }

        .report-type-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            color: var(--sicoob-cinza);
            font-weight: 600;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
            background: #f8f9fa;
        }

        .report-type-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
        }

        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }

        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-file-excel me-2"></i>Relatórios</h2>
                <p class="text-muted">Gere relatórios detalhados em Excel sobre colaboradores e cursos</p>
            </div>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_colaboradores']); ?></div>
                        <div class="stats-label">Colaboradores</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_trilhas']); ?></div>
                        <div class="stats-label">Trilhas</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['total_cursos']); ?></div>
                        <div class="stats-label">Cursos</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($estatisticas['cursos_aprovados']); ?></div>
                        <div class="stats-label">Aprovações</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Abas de Tipos de Relatório -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs report-type-tabs card-header-tabs" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="colaboradores-tab" data-bs-toggle="tab"
                                data-bs-target="#colaboradores" type="button" role="tab">
                            <i class="fas fa-users me-2"></i>Relatórios por Colaborador
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="cursos-tab" data-bs-toggle="tab"
                                data-bs-target="#cursos" type="button" role="tab">
                            <i class="fas fa-graduation-cap me-2"></i>Relatórios por Curso
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="gerenciais-tab" data-bs-toggle="tab"
                                data-bs-target="#gerenciais" type="button" role="tab">
                            <i class="fas fa-chart-bar me-2"></i>Relatórios Gerenciais
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="reportTabsContent">

                    <!-- Aba: Relatórios por Colaborador -->
                    <div class="tab-pane fade show active" id="colaboradores" role="tabpanel">
                        <h5 class="mb-4"><i class="fas fa-users me-2"></i>Relatórios por Colaborador</h5>
                        <p class="text-muted mb-4">Gere relatórios detalhados sobre colaboradores e seus cursos</p>

                        <!-- Filtros Comuns para Colaboradores -->
                        <div class="filter-section">
                            <h6><i class="fas fa-filter me-2"></i>Filtros (Opcionais)</h6>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">PA/Agência</label>
                                    <select class="form-select" id="filtro_pa_colab">
                                        <option value="">Todos os PAs</option>
                                        <?php foreach ($mapa_agencias as $agencia_id => $agencia_data): ?>
                                        <option value="<?php echo $agencia_id; ?>">
                                            <?php echo $agencia_data['numero'] . ' - ' . $agencia_data['nome']; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">Trilha</label>
                                    <select class="form-select" id="filtro_trilha_colab">
                                        <option value="">Todas as trilhas</option>
                                        <?php foreach ($trilhas_disponiveis as $trilha): ?>
                                        <option value="<?php echo htmlspecialchars($trilha); ?>">
                                            <?php echo htmlspecialchars($trilha); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">Trilhas Obrigatórias</label>
                                    <select class="form-select" id="filtro_trilha_obrigatoria_colab">
                                        <option value="">Todas</option>
                                        <option value="sim">Apenas Obrigatórias</option>
                                        <option value="nao">Apenas Não Obrigatórias</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">Função</label>
                                    <select class="form-select" id="filtro_funcao_colab">
                                        <option value="">Todas as funções</option>
                                        <?php foreach ($funcoes_disponiveis as $funcao): ?>
                                        <option value="<?php echo htmlspecialchars($funcao); ?>">
                                            <?php echo htmlspecialchars($funcao); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Período dos Prazos</label>
                                    <select class="form-select" id="filtro_periodo_colab">
                                        <option value="">Todos os períodos</option>
                                        <optgroup label="Períodos Futuros">
                                            <option value="mes_atual">Mês Atual</option>
                                            <option value="proximo_mes">Próximo Mês</option>
                                            <option value="proximos_30_dias">Próximos 30 dias</option>
                                            <option value="proximos_60_dias">Próximos 60 dias</option>
                                            <option value="proximos_90_dias">Próximos 90 dias</option>
                                        </optgroup>
                                        <optgroup label="Períodos Vencidos">
                                            <option value="vencidos_30_dias">Vencidos nos últimos 30 dias</option>
                                            <option value="vencidos_60_dias">Vencidos nos últimos 60 dias</option>
                                            <option value="vencidos_90_dias">Vencidos nos últimos 90 dias</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Opções de Relatórios por Colaborador -->
                        <div class="row">
                            <!-- Relatório Completo de Colaboradores -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-list-alt me-2 text-primary"></i>Relatório Completo de Colaboradores</h6>
                                    <p class="text-muted mb-3">Lista todos os colaboradores com informações detalhadas sobre seus cursos, status e prazos.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Dados pessoais e funcionais</li>
                                            <li>Lista de todos os cursos atribuídos</li>
                                            <li>Status de cada curso (aprovado, vencido, a vencer, em andamento)</li>
                                            <li>Prazos calculados e dias restantes</li>
                                            <li>Notas e aproveitamento</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="gerarRelatorio('colaboradores_completo')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Completo
                                    </button>
                                </div>
                            </div>

                            <!-- Colaboradores com Cursos Vencidos -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-exclamation-triangle me-2 text-danger"></i>Colaboradores com Cursos Vencidos</h6>
                                    <p class="text-muted mb-3">Lista colaboradores que possuem cursos com prazos vencidos, incluindo detalhes dos cursos.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Dados do colaborador</li>
                                            <li>Lista de cursos vencidos</li>
                                            <li>Dias de atraso</li>
                                            <li>Informações da trilha</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-danger w-100" onclick="gerarRelatorio('colaboradores_vencidos')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Vencidos
                                    </button>
                                </div>
                            </div>

                            <!-- Colaboradores com Cursos A Vencer -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-clock me-2 text-warning"></i>Colaboradores com Cursos A Vencer</h6>
                                    <p class="text-muted mb-3">Lista colaboradores que possuem cursos prestes a vencer (próximos 30 dias).</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Dados do colaborador</li>
                                            <li>Lista de cursos a vencer</li>
                                            <li>Dias restantes</li>
                                            <li>Prioridade por prazo</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-warning w-100" onclick="gerarRelatorio('colaboradores_a_vencer')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório A Vencer
                                    </button>
                                </div>
                            </div>

                            <!-- Colaboradores com Cursos Em Andamento -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-play-circle me-2 text-info"></i>Colaboradores com Cursos Em Andamento</h6>
                                    <p class="text-muted mb-3">Lista colaboradores que estão cursando atualmente (têm andamento registrado).</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Dados do colaborador</li>
                                            <li>Lista de cursos em andamento</li>
                                            <li>Etapa atual</li>
                                            <li>Progresso e prazos</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-info w-100" onclick="gerarRelatorio('colaboradores_em_andamento')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Em Andamento
                                    </button>
                                </div>
                            </div>
                            <!-- Colaboradores com Cursos Aprovados -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-check-circle me-2 text-success"></i>Colaboradores com Cursos Aprovados</h6>
                                    <p class="text-muted mb-3">Lista colaboradores que concluíram cursos com aprovação.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Dados do colaborador</li>
                                            <li>Lista de cursos aprovados</li>
                                            <li>Notas e aproveitamento</li>
                                            <li>Datas de conclusão</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="gerarRelatorio('colaboradores_aprovados')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Aprovados
                                    </button>
                                </div>
                            </div>

                            <!-- Colaboradores por Status Específico -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-filter me-2 text-primary"></i>Colaboradores por Status Personalizado</h6>
                                    <p class="text-muted mb-3">Relatório customizado com múltiplos filtros de status.</p>
                                    <div class="mb-3">
                                        <label class="form-label">Status dos Cursos (múltipla seleção)</label>
                                        <select class="form-select" id="filtro_status_personalizado" multiple size="4">
                                            <option value="aprovado">✅ Aprovado</option>
                                            <option value="em_andamento">🔄 Em Andamento</option>
                                            <option value="a_vencer">⏰ A Vencer</option>
                                            <option value="vencido">🔴 Vencido</option>
                                        </select>
                                        <small class="text-muted">Ctrl+clique para múltiplas opções</small>
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="gerarRelatorio('colaboradores_personalizado')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Personalizado
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aba: Relatórios por Curso -->
                    <div class="tab-pane fade" id="cursos" role="tabpanel">
                        <h5 class="mb-4"><i class="fas fa-graduation-cap me-2"></i>Relatórios por Curso</h5>
                        <p class="text-muted mb-4">Gere relatórios detalhados sobre cursos e seus participantes</p>

                        <!-- Filtros Comuns para Cursos -->
                        <div class="filter-section">
                            <h6><i class="fas fa-filter me-2"></i>Filtros (Opcionais)</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Curso Específico</label>
                                    <select class="form-select" id="filtro_curso_especifico">
                                        <option value="">Todos os cursos</option>
                                        <?php foreach ($cursos_disponiveis as $curso): ?>
                                        <option value="<?php echo htmlspecialchars($curso); ?>">
                                            <?php echo htmlspecialchars($curso); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Trilha</label>
                                    <select class="form-select" id="filtro_trilha_curso">
                                        <option value="">Todas as trilhas</option>
                                        <?php foreach ($trilhas_disponiveis as $trilha): ?>
                                        <option value="<?php echo htmlspecialchars($trilha); ?>">
                                            <?php echo htmlspecialchars($trilha); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Trilhas Obrigatórias</label>
                                    <select class="form-select" id="filtro_trilha_obrigatoria_curso">
                                        <option value="">Todas</option>
                                        <option value="sim">Apenas Obrigatórias</option>
                                        <option value="nao">Apenas Não Obrigatórias</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Status dos Participantes</label>
                                    <select class="form-select" id="filtro_status_curso">
                                        <option value="">Todos os status</option>
                                        <option value="aprovado">✅ Aprovados</option>
                                        <option value="em_andamento">🔄 Em Andamento</option>
                                        <option value="a_vencer">⏰ A Vencer</option>
                                        <option value="vencido">🔴 Vencidos</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Opções de Relatórios por Curso -->
                        <div class="row">
                            <!-- Relatório Simples de Cursos -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-tachometer-alt me-2 text-success"></i>Relatório Simples de Cursos</h6>
                                    <p class="text-muted mb-3">Visão geral otimizada com estatísticas consolidadas (recomendado).</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Estatísticas por curso</li>
                                            <li>Contadores de participantes</li>
                                            <li>Percentuais de aprovação</li>
                                            <li>Performance otimizada</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="gerarRelatorio('cursos_simples')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Simples
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório Completo de Cursos -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-list-alt me-2 text-primary"></i>Relatório Completo de Cursos</h6>
                                    <p class="text-muted mb-3">Lista todos os cursos com estatísticas detalhadas e lista de participantes.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Informações do curso e trilha</li>
                                            <li>Lista completa de participantes</li>
                                            <li>Status de cada participante</li>
                                            <li>Estatísticas de aprovação</li>
                                            <li>Médias de notas e aproveitamento</li>
                                        </ul>
                                        <div class="alert alert-warning alert-sm mt-2 mb-0">
                                            <small><i class="fas fa-exclamation-triangle me-1"></i>Pode ser lento para muitos cursos</small>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-primary w-100" onclick="gerarRelatorio('cursos_completo')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Completo
                                    </button>
                                </div>
                            </div>

                            <!-- Cursos com Participantes Vencidos -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-exclamation-triangle me-2 text-danger"></i>Cursos com Participantes Vencidos</h6>
                                    <p class="text-muted mb-3">Lista cursos que possuem colaboradores com prazos vencidos.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Informações do curso</li>
                                            <li>Lista de colaboradores vencidos</li>
                                            <li>Dias de atraso por colaborador</li>
                                            <li>Estatísticas de vencimento</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-danger w-100" onclick="gerarRelatorio('cursos_vencidos')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Vencidos
                                    </button>
                                </div>
                            </div>

                            <!-- Cursos com Participantes A Vencer -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-clock me-2 text-warning"></i>Cursos com Participantes A Vencer</h6>
                                    <p class="text-muted mb-3">Lista cursos que possuem colaboradores com prazos próximos ao vencimento.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Informações do curso</li>
                                            <li>Lista de colaboradores a vencer</li>
                                            <li>Dias restantes por colaborador</li>
                                            <li>Priorização por urgência</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-warning w-100" onclick="gerarRelatorio('cursos_a_vencer')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório A Vencer
                                    </button>
                                </div>
                            </div>

                            <!-- Cursos com Participantes Em Andamento -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-play-circle me-2 text-info"></i>Cursos com Participantes Em Andamento</h6>
                                    <p class="text-muted mb-3">Lista cursos que possuem colaboradores atualmente cursando.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Informações do curso</li>
                                            <li>Lista de colaboradores em andamento</li>
                                            <li>Etapa atual de cada colaborador</li>
                                            <li>Progresso e estimativas</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-info w-100" onclick="gerarRelatorio('cursos_em_andamento')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Em Andamento
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aba: Relatórios Gerenciais -->
                    <div class="tab-pane fade" id="gerenciais" role="tabpanel">
                        <h5 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Relatórios Gerenciais</h5>
                        <p class="text-muted mb-4">Relatórios consolidados para gestão e tomada de decisões</p>

                        <!-- Opções de Relatórios Gerenciais -->
                        <div class="row">
                            <!-- Dashboard Executivo -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-chart-pie me-2 text-primary"></i>Dashboard Executivo</h6>
                                    <p class="text-muted mb-3">Visão geral consolidada com principais métricas e indicadores.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Resumo geral de colaboradores e cursos</li>
                                            <li>Percentuais de aprovação por trilha</li>
                                            <li>Distribuição por PA/Agência</li>
                                            <li>Indicadores de performance</li>
                                            <li>Tendências e alertas</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="gerarRelatorio('dashboard_executivo')">
                                        <i class="fas fa-download me-2"></i>Gerar Dashboard Executivo
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório por PA/Agência -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-building me-2 text-info"></i>Relatório por PA/Agência</h6>
                                    <p class="text-muted mb-3">Análise detalhada por Ponto de Atendimento com comparativos.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Estatísticas por PA</li>
                                            <li>Ranking de performance</li>
                                            <li>Comparativo entre agências</li>
                                            <li>Identificação de oportunidades</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-info w-100" onclick="gerarRelatorio('relatorio_por_pa')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório por PA
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório de Prazos e Vencimentos -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-calendar-times me-2 text-warning"></i>Relatório de Prazos e Vencimentos</h6>
                                    <p class="text-muted mb-3">Análise temporal de prazos com projeções e alertas.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Cronograma de vencimentos</li>
                                            <li>Projeções para próximos meses</li>
                                            <li>Histórico de cumprimento</li>
                                            <li>Alertas de risco</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-warning w-100" onclick="gerarRelatorio('prazos_vencimentos')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Prazos
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório de Performance por Trilha -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-route me-2 text-success"></i>Performance por Trilha</h6>
                                    <p class="text-muted mb-3">Análise de efetividade e performance das trilhas de aprendizagem.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Taxa de conclusão por trilha</li>
                                            <li>Tempo médio de conclusão</li>
                                            <li>Notas e aproveitamento médio</li>
                                            <li>Identificação de gargalos</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="gerarRelatorio('performance_trilhas')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Performance
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório de Compliance -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-shield-alt me-2 text-danger"></i>Relatório de Compliance</h6>
                                    <p class="text-muted mb-3">Monitoramento de conformidade e cumprimento de obrigações.</p>
                                    <div class="mb-3">
                                        <strong>Inclui:</strong>
                                        <ul class="small text-muted mb-0">
                                            <li>Status de conformidade por colaborador</li>
                                            <li>Cursos obrigatórios pendentes</li>
                                            <li>Riscos de não conformidade</li>
                                            <li>Planos de ação recomendados</li>
                                        </ul>
                                    </div>
                                    <button class="btn btn-danger w-100" onclick="gerarRelatorio('compliance')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório de Compliance
                                    </button>
                                </div>
                            </div>

                            <!-- Relatório Personalizado Avançado -->
                            <div class="col-lg-6 mb-4">
                                <div class="report-option">
                                    <h6><i class="fas fa-cogs me-2 text-secondary"></i>Relatório Personalizado Avançado</h6>
                                    <p class="text-muted mb-3">Configure filtros avançados para relatórios sob medida.</p>
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-12 mb-2">
                                                <label class="form-label small">Período de Análise</label>
                                                <select class="form-select form-select-sm" id="periodo_analise">
                                                    <option value="mes_atual">Mês Atual</option>
                                                    <option value="trimestre">Trimestre Atual</option>
                                                    <option value="semestre">Semestre Atual</option>
                                                    <option value="ano">Ano Atual</option>
                                                    <option value="personalizado">Período Personalizado</option>
                                                </select>
                                            </div>
                                            <div class="col-12 mb-2">
                                                <label class="form-label small">Agrupamento</label>
                                                <select class="form-select form-select-sm" id="agrupamento">
                                                    <option value="pa">Por PA/Agência</option>
                                                    <option value="trilha">Por Trilha</option>
                                                    <option value="funcao">Por Função</option>
                                                    <option value="status">Por Status</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-secondary w-100" onclick="gerarRelatorio('personalizado_avancado')">
                                        <i class="fas fa-download me-2"></i>Gerar Relatório Personalizado
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <h5>Gerando Relatório</h5>
            <p class="text-muted mb-0">Por favor, aguarde enquanto processamos os dados...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function gerarRelatorio(tipoRelatorio) {
            // Mostrar loading
            document.getElementById('loadingOverlay').style.display = 'block';

            // Coletar filtros baseado na aba ativa
            const abaAtiva = document.querySelector('.tab-pane.active').id;
            let filtros = {};

            if (abaAtiva === 'colaboradores') {
                filtros = {
                    pa: document.getElementById('filtro_pa_colab').value,
                    trilha: document.getElementById('filtro_trilha_colab').value,
                    trilha_obrigatoria: document.getElementById('filtro_trilha_obrigatoria_colab').value,
                    funcao: document.getElementById('filtro_funcao_colab').value,
                    periodo: document.getElementById('filtro_periodo_colab').value
                };

                if (tipoRelatorio === 'colaboradores_personalizado') {
                    const statusSelect = document.getElementById('filtro_status_personalizado');
                    const selectedValues = [];
                    for (let i = 0; i < statusSelect.options.length; i++) {
                        if (statusSelect.options[i].selected) {
                            selectedValues.push(statusSelect.options[i].value);
                        }
                    }
                    filtros.status = selectedValues;

                    // Debug: mostrar no console o que foi selecionado
                    console.log('Status selecionados:', selectedValues);

                    // Validar se pelo menos um status foi selecionado
                    if (selectedValues.length === 0) {
                        alert('Por favor, selecione pelo menos um status para gerar o relatório personalizado.');
                        document.getElementById('loadingOverlay').style.display = 'none';
                        return;
                    }
                }
            } else if (abaAtiva === 'cursos') {
                filtros = {
                    curso: document.getElementById('filtro_curso_especifico').value,
                    trilha: document.getElementById('filtro_trilha_curso').value,
                    trilha_obrigatoria: document.getElementById('filtro_trilha_obrigatoria_curso').value,
                    status: document.getElementById('filtro_status_curso').value
                };
            } else if (abaAtiva === 'gerenciais') {
                if (tipoRelatorio === 'personalizado_avancado') {
                    filtros = {
                        periodo_analise: document.getElementById('periodo_analise').value,
                        agrupamento: document.getElementById('agrupamento').value
                    };
                }
            }

            // Construir URL para download
            const params = new URLSearchParams();
            params.append('tipo', tipoRelatorio);

            // Adicionar filtros, tratando arrays especialmente
            for (const [key, value] of Object.entries(filtros)) {
                if (Array.isArray(value)) {
                    // Para arrays, converter para string separada por vírgula
                    if (value.length > 0) {
                        params.append(key, value.join(','));
                    }
                } else if (value !== '' && value !== null && value !== undefined) {
                    params.append(key, value);
                }
            }

            // Redirecionar para geração do relatório
            window.location.href = 'gerar_relatorio.php?' + params.toString();

            // Esconder loading após um tempo
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
            }, 3000);
        }

        // Função para limpar filtros
        function limparFiltros() {
            const selects = document.querySelectorAll('.tab-pane.active select');
            selects.forEach(select => {
                select.selectedIndex = 0;
            });
        }

        // Adicionar botão de limpar filtros em cada aba
        document.addEventListener('DOMContentLoaded', function() {
            const filterSections = document.querySelectorAll('.filter-section');
            filterSections.forEach(section => {
                const clearButton = document.createElement('button');
                clearButton.type = 'button';
                clearButton.className = 'btn btn-outline-secondary btn-sm mt-2';
                clearButton.innerHTML = '<i class="fas fa-eraser me-1"></i>Limpar Filtros';
                clearButton.onclick = limparFiltros;
                section.appendChild(clearButton);
            });
        });
    </script>
</body>
</html>
